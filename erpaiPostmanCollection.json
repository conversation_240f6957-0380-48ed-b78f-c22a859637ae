{"info": {"name": " erpai Collection", "description": "erpai collection BAAS", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_variable_scope": "environment", "_postman_exported_at": "2024-09-20T14:53:46.803Z", "_postman_exported_using": "BAAS"}, "item": [{"name": "Admin Default Departments", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/admin/default-departments", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/admin/default-departments"]}, "body": {}, "description": ""}, "response": []}, {"name": "Super Admin Dashboard", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/admin/dashboard", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/admin/dashboard"]}, "body": {}, "description": ""}, "response": []}, {"name": "Finish Assesment Setup", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/finish-setup", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/finish-setup"], "variable": [{"key": "assesment_id", "value": "", "description": ""}]}, "body": {}, "description": ""}, "response": []}, {"name": "Save Assesment Length and Frequency", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/assesment-settings", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/assesment-settings"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Generate Areas (Chatbot)", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/get-areas", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/get-areas"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Save Tags for Areas", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/save-areas", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/save-areas"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Detailed Insights", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/detailed-insight", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/detailed-insight"], "variable": [{"key": "assesment_id", "value": "", "description": ""}]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Get Employee Details", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/get-employee-details", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/get-employee-details"], "variable": [{"key": "assesment_id", "value": "", "description": ""}]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Timeline Graph Chart", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/get-timeline-graph", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/get-timeline-graph"], "variable": [{"key": "assesment_id", "value": "", "description": ""}]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Get Assesments", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/common/get-assesments", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/common/get-assesments"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Choose Departments", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/department-selection", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/department-selection"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Package recommendation ", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/get-pacakege-recommendation", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/get-pacakege-recommendation"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Save Acoount Set Up Questionaire", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/account-setup-questionaire", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/account-setup-questionaire"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Viewer <PERSON>", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/viwer/login-with-token", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/viwer/login-with-token"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "User Login", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/user/login", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/user/login"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Get Roles And Positions", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/roles-and-positions", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/roles-and-positions"]}, "body": {}, "description": ""}, "response": []}, {"name": "Add Team Member", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/add-team-member", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/add-team-member"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Save SOP Template", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/sop-save", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/sop-save"]}, "body": {}, "description": ""}, "response": []}, {"name": "Get SOP Questionaire", "request": {"method": "GET", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/sop-questionaire", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/sop-questionaire"]}, "body": {}, "description": ""}, "response": []}, {"name": "Generate SOP", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/generate-sop", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/generate-sop"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Finalize Team", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/team-form", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/team-form"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Extract From Questionaire", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/extract-questionaire", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/extract-questionaire"]}, "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "description": ""}, "response": []}, {"name": "Extract From SOW", "request": {"method": "POST", "header": [{"key": "x-project", "value": "ZXJwYWk6bGE5eGU5bXU2amcweGszemJkNzY1cGY5c3hkMXJjbzR0dQ==", "type": "text"}, {"key": "Authorization", "value": "Bearer <your_token_here>", "type": "text"}], "url": {"raw": "{{baseurl}}/v3/api/custom/erpai/company/extract-document", "host": ["{{baseurl}}"], "path": ["/v3/api/custom/erpai/company/extract-document"], "variable": [{"key": "document_link", "value": "", "description": ""}]}, "body": {}, "description": ""}, "response": []}], "variable": [{"key": "baseurl", "value": "https://erpai.mkdlabs.com", "type": "string"}]}