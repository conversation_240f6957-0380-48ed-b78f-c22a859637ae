

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}


async function checkAndRetryEntries(sdk, job_id) {
    sdk.setTable("entry");
    let entries = await sdk.get({ job_id: job_id, status: 0 });

    if (entries.length != 0) {

        sdk.setTable("job");

        await sdk.insert({
            task: "handleItems",
            identifier: "job_id_" + job_id,
            arguments: JSON.stringify({ job_id: job_id }),
            status: "pending",
            time_interval: "once",
            retry_count: 3
        });

        return false;
    }

    return true;
}


async function handleItems(sdk, options) {
    console.log("Handling items")

    try {
        let { job_id } = options;
        sdk.setTable("job_details")
        let job = await sdk.get({ id: job_id })
        console.log(job)
        if (job.length == 0) {
            return { error: true, message: "Job not found" }
        }
        sdk.setTable("item")
        let items = await sdk.get({ job_id: job_id })

        console.log(items)
        let promises = [];
        let errors = [];
        for (let item of items) {
            console.log("Handling item: ", item.id);
            if (item.status == 1) {
                continue;
            }

            count = item.retry_count ?? 0;
            if (count >= 5) {
                await sdk.update({status: 2}, item.id);
                continue;
            }

            await sdk.update({retry_count: count+1}, item.id);

            promises.push(
                callLabelService(item).catch(error => {
                    errors.push({ id: item.id, error });
                })
            );

            if (promises.length === concurrent_count) {
                await Promise.all(promises);
                promises = [];
            }
        }

        // Process any remaining promises
        if (promises.length > 0) {
            await Promise.all(promises);
        }

        // Print errors after all promises have been executed
        if (errors.length > 0) {
            console.error("Errors occurred:");
            errors.forEach(({ id, error }) => {
                console.error(`Item ID ${id}: `, error);
            });
        }

        let itemsHandled = await checkAndRetryEntries(sdk, job_id);
        if (!itemsHandled) {
            return { error: false, message: "Items not handled" }
        }

        sdk.setTable("job_details")
        await sdk.update({ status: 2 }, job_id)

        return { error: false, message: "Items handled" }

    }
    catch (error) {
        console.error("Error occurred: ", error);
        return { error: true, message: "Error occurred" }
    }
    
}

module.exports = handleEntry;
