


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */



    const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
    const UrlMiddleware = require("../../../middleware/UrlMiddleware");
    const HostMiddleware = require("../../../middleware/HostMiddleware");
    const TokenMiddleware = require("../../../middleware/TokenMiddleware");
    const { sqlDateTimeFormat, formatTime } = require("../../../services/UtilService");
    const rateLimit = require("express-rate-limit");
    const { stringChecker } = require("../../../utils/helpers");

    const RateLimitMiddleware = rateLimit({
        windowMs: 2 * 60 * 1000, // 2 minutes
        max: 100, // Limit each IP to 5 requests per window (here, per hour)
        standardHeaders: true, // Return rate limit info in the RateLimit-* headers
        legacyHeaders: false, // Disable the X-RateLimit-* headers
    });

    const middlewares = [
        ProjectMiddleware, 
        // UrlMiddleware, 
        // HostMiddleware,
        RateLimitMiddleware
    ];

    const config = require("../../../config");





const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

