


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */


const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const {sqlDateFormat, sqlDateTimeFormat} = require("../../../services/UtilService")

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const publicMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

const config = require("../../../config");



async function includeTagsAndCategories(blogs, sdk){
    for(let blog of blogs){
      let tags = await sdk.join('blog_post_tags', 'blog_tags', 'tag_id', 'id', 'name, blog_tags.id', {post_id: blog.id});
      let categories = await sdk.join('blog_post_category', 'blog_category', 'category_id', 'id', 'name, blog_category.id', {post_id: blog.id});
      sdk.setTable('blog_post_views');
      let views = await sdk.get({post_id: blog.id })
      blog.tags = tags,
      blog.categories = categories
      blog.views = views.length
    }

    return blogs;
  }


  async function recursiveDeleteCategory(category_id, sdk){
    sdk.setTable('blog_category');
    let children = await sdk.get({parent_id: category_id});

    await sdk.deleteWhere({
        id: category_id
    });

    for(let x of children){
         recursiveDeleteCategory(x.id, sdk);
    }
}



const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

