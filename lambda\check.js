


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */


  const TokenMiddleware = require("../../../middleware/TokenMiddleware");
  const config = require("../../../config");



const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

