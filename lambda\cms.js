


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */


const ProjectMiddleware = require("/../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../services/UtilService");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const publicMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

const config = require("../../../config");



const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            app.post("/v2/api/lambda/cms", middlewares, async function (req, res) {
          try {
            let client = req.app.get("subscriber");
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.body.page) {
              return res.status(403).json({
                error: true,
                message: "Page Missing",
                validation: { page: "page missing" },
              });
            }
      
            if (!req.body.key) {
              return res.status(403).json({
                error: true,
                message: "Key Missing",
                validation: { key: "key missing" },
              });
            }
      
            if (!req.body.type) {
              return res.status(403).json({
                error: true,
                message: "Type Missing",
                validation: { type: "type missing" },
              });
            }
      
            if (!req.body.value) {
              return res.status(403).json({
                error: true,
                message: "Value Missing",
                validation: { value: "value missing" },
              });
            }
      
            const insertResult = await sdk.insert({
              page: req.body.page,
              content_type: req.body.type,
              content_key: req.body.key,
              content_value: req.body.value,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date()),
            });
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageResult = await sdk.get({
              page: req.body.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page, JSON.stringify(getAllPageResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageKeyResult = await sdk.get({
              page: req.body.page,
              content_key: req.body.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page + "-" + req.body.key, JSON.stringify(getAllPageKeyResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).json({
              error: false,
              message: insertResult,
            });
          } catch (error) {
            console.log(error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.put("/v2/api/lambda/cms/:id", middlewares, async function (req, res) {
          try {
            let client = req.app.get("subscriber");
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.body.page) {
              return res.status(403).json({
                error: true,
                message: "Page Missing",
                validation: { page: "page missing" },
              });
            }
      
            if (!req.body.key) {
              return res.status(403).json({
                error: true,
                message: "Key Missing",
                validation: { key: "key missing" },
              });
            }
      
            if (!req.body.type) {
              return res.status(403).json({
                error: true,
                message: "Type Missing",
                validation: { type: "type missing" },
              });
            }
      
            if (!req.body.value) {
              return res.status(403).json({
                error: true,
                message: "Value Missing",
                validation: { value: "value missing" },
              });
            }
      
            const updateResult = await sdk.update(
              {
                page: req.body.page,
                content_type: req.body.type,
                content_key: req.body.key,
                content_value: req.body.value,
                update_at: sqlDateTimeFormat(new Date()),
              },
              req.params.id
            );
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageResult = await sdk.get({
              page: req.body.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page, JSON.stringify(getAllPageResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            const getAllPageKeyResult = await sdk.get({
              page: req.body.page,
              content_key: req.body.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.body.page + "-" + req.body.key, JSON.stringify(getAllPageKeyResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).json({
              error: false,
              message: updateResult,
            });
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.delete("/v2/api/lambda/cms/:id", middlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.params.id) {
              return res.status(403).json({
                error: true,
                message: "ID Missing",
                validation: { id: "id missing" },
              });
            }
      
            await sdk.delete({}, req.params.id);
      
            return res.status(200).json({
              error: false,
              message: "deleted",
            });
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.get("/v2/api/lambda/cms/id/:id", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            if (!req.params.id) {
              return res.status(403).json({
                error: true,
                message: "ID Missing",
                validation: { id: "id missing" },
              });
            }
      
            const result = await sdk.get({ id: req.params.id });
      
            if (result.length > 0) {
              return res.status(200).json({
                error: false,
                model: result[0],
              });
            } else {
              return res.status(200).json({
                error: false,
                model: null,
              });
            }
          } catch (error) {
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.get("/v2/api/lambda/cms/page/:page/:key", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId + "-" + req.params.page + "-" + req.params.key);
      
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({
              page: req.params.page,
              content_key: req.params.key,
            });
      
            client.set("cms-" + req.projectId + "-" + req.params.page + "-" + req.params.key, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.get("/v2/api/lambda/cms/page/:page", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId + "-" + req.params.page);
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({
              page: req.params.page,
            });
      
            client.set("cms-" + req.projectId + "-" + req.params.page, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            
              
            //Endpoint
            /*
            app.get("/v2/api/lambda/cms/all", publicMiddlewares, async function (req, res) {
          try {
            let sdk = req.sdk;
            sdk.setProjectId(req.projectId);
            sdk.setTable("cms");
      
            let client = req.app.get("subscriber");
            const getall = await client.get("cms-" + req.projectId);
            if (getall) {
              return res.status(200).send(getall);
            }
      
            const getAllResult = await sdk.get({});
      
            client.set("cms-" + req.projectId, JSON.stringify(getAllResult), "EX", 60 * 60, function (data) {
              console.log("SET", data);
            });
      
            return res.status(200).send(getAllResult);
          } catch (error) {
            console.log("ERROR: ", error);
            return res.status(403).json({
              error: true,
              message: "Something went wrong",
            });
          }
        });
            */
    
            


    return [];
}

