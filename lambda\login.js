


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */


const AuthService = require("../../../services/AuthService");
const JwtService = require("../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const LogMiddleware = require("../../../middleware/LogMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const RateLimitMiddleware = require("../../../middleware/RateLimitMiddleware");
const DevLogService = require("../../../services/DevLogService");
const ValidationService = require("../../../services/ValidationService");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  // HostMiddleware,
  // PermissionMiddleware,
  RateLimitMiddleware,
  LogMiddleware
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();

const config = require("../../../config");



const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

