


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */

const AuthService = require("../../../services/AuthService");
const JwtService = require("../../../services/JwtService");
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const PermissionMiddleware = require("../../../middleware/PermissionMiddleware");
const permissionService = require("../../../services/PermissionService");
const DevLogService = require("../../../services/DevLogService");
const config = require("../../../config");
const NodeGoogleLogin = require("node-google-login");
const ManaKnightSDK = require("../../../core/ManaKnightSDK");
const BackendSDK = require("../../../core/BackendSDK");
const { ideahub_v1alpha } = require("googleapis");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  PermissionMiddleware
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();


const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

