


/**
 * So when we save the new lambda, we save the file path to server and we just read this file.
 * Then we trigger reload of server somehow.
 * @param {*} app
 */

const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const DevLogService = require("../../../services/DevLogService");
const UploadService = require("../../../services/UploadService");
const { getLocalPath, sqlDateFormat, sqlDateTimeFormat, sizeOfRemote } = require("../../../services/UtilService");
const sizeOf = require("image-size");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const upload = UploadService.local_upload();
const uploadS3 = UploadService.s3_upload();
const uploadS3Public = UploadService.s3_upload_public();
const fs = require("fs");

const imageMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  upload.single("file"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imageMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  uploadS3.single("file"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imagesMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  upload.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];
const imagesMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  uploadS3.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

const imagesPublicMiddlewaresS3 = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  uploadS3Public.array("files"),
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

let logService = new DevLogService();



const config = require("../../../config");



module.exports = function (app) {    

    
              
            //Endpoint
            /*
            
            */
    
            
              
            //Endpoint
            /*
            
            */
    
            


    return [];
}

