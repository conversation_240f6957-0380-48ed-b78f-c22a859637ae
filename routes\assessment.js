const { middlewares, TokenMiddleware } = require("../utils/middleware");
const { calculatePeriodId } = require("../service/calculations");
const { sqlDateTimeFormat } = require("../../../services/UtilService");
const { sqlDateFormat } = require("../../../services/UtilService");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  // Assessment routes will be extracted here
  // This includes endpoints like:
  app.post("/v3/api/custom/erpai/assessment/close-period", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id, period_id, note = null, user_id = null, reason = null, type } = req.body;

      // Validate required fields
      if (!assessment_id || !period_id || type === undefined) {
        return res.status(400).json({
          error: true,
          message: "Missing required fields"
        });
      }

      // Check if period is already closed or completed
      sdk.setTable("assessment_closing_period");
      let existing_closure = await sdk.get({ assessment_id, period_id });
      if (existing_closure.length > 0) {
        return res.status(400).json({
          error: true,
          message: "This period has already been marked as completed or closed"
        });
      }

      // Get current period
      let current_period = await calculatePeriodId(sdk, assessment_id);

      // Check if trying to close a future period
      if (period_id > current_period) {
        return res.status(400).json({
          error: true,
          message: "Cannot close or complete a future period"
        });
      }

      // Get all questions for this assessment up to current period that aren't answered 'Yes'
      let incompleteAssessment = await sdk.rawQuery(`
        WITH assessment_status AS (
          SELECT 
            COUNT(*) as total_questions,
            COUNT(CASE WHEN answer = 'Yes' THEN 1 END) as completed_questions
          FROM erpai_assessment_item
          WHERE assessment_id = ${assessment_id}
          AND period_id = ${period_id}
        )
        SELECT 
          total_questions,
          completed_questions,
          (total_questions = completed_questions) as is_fully_completed
        FROM assessment_status
      `);

      // If marking as completed, check if all questions up to this period are answered 'Yes'
      if (type === 1 && !incompleteAssessment[0].is_fully_completed) {
        return res.status(400).json({
          error: true,
          message: "Cannot mark period as completed - all questions must be answered 'Yes'",
          total_questions: incompleteAssessment[0].total_questions,
          completed_questions: incompleteAssessment[0].completed_questions,
          remaining_questions: incompleteAssessment[0].total_questions - incompleteAssessment[0].completed_questions
        });
      }

      // Get unanswered items for the current period
      sdk.setTable("assessment_item");
      let unanswered_items = await sdk.rawQuery(`
        SELECT * FROM erpai_assessment_item
        WHERE assessment_id = ${assessment_id} 
        AND period_id = ${period_id} 
        AND (answer IS NULL OR answer != 'Yes')
      `);

      // Insert closure record
      sdk.setTable("assessment_closing_period");
      await sdk.insert({
        assessment_id,
        period_id,
        note,
        user_id,
        reason,
        type,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // If closing period (type = 0), move items that are not answered as 'Yes' to next period
      if (type === 0 && unanswered_items.length > 0) {
        sdk.setTable("assessment_item");
        // check duplicate of items in the next period and delete it
        let items_in_next_period = await sdk.get({
          assessment_id,
          period_id: current_period + 1
        });

        for (let item of unanswered_items) {
          if (items_in_next_period.length > 0) {
            let item_to_move = items_in_next_period.filter((next_item) => next_item.question === item.question);
            // move over root_cause and correction_action from the old item to the new item

            if (item_to_move.length > 0) {
              sdk.setTable("assessment_item");
              await sdk.update(
                {
                  root_cause_analysis: item.root_cause_analysis,
                  correction_action: item.correction_action
                },
                item_to_move[0].id
              );
            }
          }
        }

        for (let item of unanswered_items) {
          sdk.setTable("assessment_item");
          await sdk.update(
            {
              moved: 1,
              answered_period: null,
              critical: false,
              answer: null,
              dependency_id: null,
              root_cause_analysis: null,
              correction_action: null,
              chatbot_verification: null,
              general_notes: null
            },
            item.id
          );
        }
      }

      return res.status(200).json({
        error: false,
        message: type === 1 ? "Period marked as completed successfully" : "Period closed successfully"
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v3/api/custom/erpai/assessment/files", [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const role = req.role;
      let company_id;

      // Get company_id based on role
      if (role === "company") {
        company_id = user_id;
      } else {
        sdk.setTable("user");
        const user = await sdk.get({ id: user_id });
        company_id = user[0].company;
      }

      // Get query parameters
      const { assessment_id, area_id, period_id, from_date, to_date, critical_only = false, page = 1, limit = 10 } = req.query;

      // Build the base query with proper joins
      let query = `
  WITH calculated AS (
    SELECT *, 
    CASE 
      WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 1) + 1, duration)
      WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 7) + 1, duration)
      WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 30) + 1, duration)
      WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 365) + 1, duration)
      ELSE NULL
    END as initial_current_period
    FROM erpai_assessment
    WHERE company_id = ${company_id}
  ),
  closed_periods AS (
    SELECT assessment_id, MAX(period_id) as max_closed_period
    FROM erpai_assessment_closing_period
    GROUP BY assessment_id
  ),
  adjusted_period AS (
    SELECT c.*,
    CASE
      WHEN EXISTS (
        SELECT 1 FROM closed_periods cp
        WHERE cp.assessment_id = c.id
      ) THEN 
        (SELECT LEAST(cp.max_closed_period + 1, c.duration)
         FROM closed_periods cp
         WHERE cp.assessment_id = c.id)
      ELSE c.initial_current_period
    END as current_period
    FROM calculated c
  )
  SELECT 
    a.id as assessment_id,
    a.name as assessment_name,
    area.id as area_id,
    area.name as area_name,
    ai.id as question_id,
    ai.question,
    ai.critical,
    ai.period_id,
    a.document_link,
    ai.create_at,
    u.first_name,
    u.last_name,
    p.department_id,
    d.name as department_name
  FROM 
    erpai_assessment_item ai
  JOIN 
    erpai_assessment a ON ai.assessment_id = a.id
  JOIN 
    erpai_assessment_area area ON ai.area_id = area.id
  JOIN
    adjusted_period ap ON a.id = ap.id
  LEFT JOIN
    erpai_user u ON ai.assigned_to = u.id
  LEFT JOIN
    erpai_position p ON u.position_id = p.id
  LEFT JOIN
    erpai_department d ON p.department_id = d.id
  WHERE 
    a.company_id = ${company_id}
    AND a.document_link IS NOT NULL
    ${assessment_id ? `AND a.id = ${assessment_id}` : ""}
    ${area_id ? `AND area.id = ${area_id}` : ""}
    ${period_id ? `AND ai.period_id = ${period_id}` : ""}
    ${critical_only === "true" ? "AND ai.critical = 1" : ""}
    ${from_date ? `AND DATE(ai.create_at) >= '${from_date}'` : ""}
    ${to_date ? `AND DATE(ai.create_at) <= '${to_date}'` : ""}
`;

      // If manager, only show their department's files
      if (role === "manager") {
        const manager = await sdk.get({ id: user_id });
        query += ` AND u.department_id = ${manager[0].department_id}`;
      }
      // If worker, only show their own files
      else if (role === "worker") {
        query += ` AND ai.assigned_to = ${user_id}`;
      }

      // Add sorting and pagination
      query += `
          ORDER BY ai.create_at DESC
          LIMIT ${(page - 1) * limit}, ${limit}
        `;

      // Get total count for pagination
      const countQuery = `SELECT COUNT(*) as total FROM (${query.split("LIMIT")[0]}) as subquery`;
      const [{ total }] = await sdk.rawQuery(countQuery);

      // Get the paginated results
      const files = await sdk.rawQuery(query);

      // Group files by assessment and area
      const groupedFiles = files.reduce((acc, file) => {
        if (!acc[file.assessment_id]) {
          acc[file.assessment_id] = {
            assessment_id: file.assessment_id,
            assessment_name: file.assessment_name,
            areas: {}
          };
        }
        if (!acc[file.assessment_id].areas[file.area_id]) {
          acc[file.assessment_id].areas[file.area_id] = {
            area_id: file.area_id,
            area_name: file.area_name,
            files: []
          };
        }
        acc[file.assessment_id].areas[file.area_id].files.push({
          question_id: file.question_id,
          question: file.question,
          critical: file.critical,
          period_id: file.period_id,
          document_link: file.document_link,
          create_at: file.create_at,
          uploaded_by: {
            user_id: file.assigned_to,
            name: `${file.first_name} ${file.last_name}`,
            department: file.department_name
          }
        });
        return acc;
      }, {});

      return res.status(200).json({
        error: false,
        message: "Files retrieved successfully",
        data: {
          assessments: Object.values(groupedFiles).map((assessment) => ({
            ...assessment,
            areas: Object.values(assessment.areas)
          })),
          pagination: {
            total,
            page: parseInt(page),
            limit: parseInt(limit),
            total_pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  // -

  app.get("/v3/api/custom/erpai/assessment/items", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const { assessment_id, critical, answered, period_id, assigned_to, include_moved } = req.query;

      if (!assessment_id) {
        return res.status(400).json({
          error: true,
          message: "Assessment ID is required"
        });
      }

      // Build the query conditions
      let conditions = [`ai.assessment_id = ${assessment_id}`, `ai.moved = ${include_moved === "true" ? 1 : 0}`];
      let params = [];

      if (critical !== undefined) {
        conditions.push(`critical = ${critical === "1" ? 1 : 0}`);
      }

      if (answered !== undefined) {
        if (answered === "yes") {
          conditions.push(`answer = 'Yes'`);
        } else if (answered === "no") {
          conditions.push(`(answer = 'No' OR answer IS NULL)`);
        }
      }

      if (period_id !== undefined) {
        conditions.push(`period_id = ${period_id}`);
      }

      if (assigned_to !== undefined) {
        conditions.push(`assigned_to = ${assigned_to}`);
      }

      // Construct and execute the query
      const items = await sdk.rawQuery(`
        SELECT 
          ai.*,
          area.name as area_name,
          area.id as area_id
        FROM erpai_assessment_item ai
        LEFT JOIN erpai_assessment_area area ON ai.area_id = area.id
        WHERE ${conditions.join(" AND ")}
        ORDER BY ai.create_at DESC
      `);

      return res.status(200).json({
        error: false,
        data: items
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/erpai/assessment/answer-question/:question_id", [...middlewares, TokenMiddleware({ role: "worker|manager" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const { question_id } = req.params;
      const { answer, critical } = req.body;
      const user_id = req.user_id;
      const role = req.role;

      // Validate input
      if (!answer) {
        return res.status(400).json({
          error: true,
          message: "Answer is required"
        });
      }

      // Get question details with assessment info and current period
      const questionData = await sdk.rawQuery(`
         WITH calculated AS (
  SELECT *, 
    CASE 
      WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 1) + 1, duration)
      WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 7) + 1, duration)
      WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 30) + 1, duration)
      WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 365) + 1, duration)
      ELSE NULL
    END as initial_current_period
  FROM erpai_assessment
),

closed_periods AS (
  SELECT period_id
  FROM erpai_assessment_closing_period
  WHERE assessment_id IN (SELECT id FROM calculated)  -- Fixing the alias issue
),

adjusted_period AS (
  SELECT 
    c.*,
    (
      SELECT COALESCE(MAX(period_id) + 1, c.initial_current_period)
      FROM erpai_assessment_closing_period
      WHERE assessment_id = c.id AND period_id < c.duration
    ) as current_period
  FROM calculated c
)

SELECT 
  ai.*,
  a.company_id,
  a.role_slug,
  ap.current_period,
  d.id as department_id,
  d.name as department_name
FROM 
  erpai_assessment_item ai
JOIN 
  erpai_assessment a ON ai.assessment_id = a.id
JOIN 
  adjusted_period ap ON a.id = ap.id
LEFT JOIN
  erpai_department d ON a.department_id = d.id
WHERE 
  ai.id = ${question_id}
        `);

      if (!questionData.length) {
        return res.status(404).json({
          error: true,
          message: "Question not found"
        });
      }

      const question = questionData[0];

      // Check if question's period has passed
      if (question.period_id < question.current_period) {
        return res.status(403).json({
          error: true,
          message: "Assessment Question period has been closed"
        });
      }

      // Verify user has permission to answer this question
      if (role === "worker") {
        if (question.assigned_to !== user_id) {
          return res.status(403).json({
            error: true,
            message: "You can only answer questions assigned to you"
          });
        }
      } else if (role === "manager") {
        // Get manager's department
        sdk.setTable("user");
        const manager = await sdk.get({ id: user_id });
        if (question.role_slug === "manager" && question.department_id !== manager[0].department_id) {
          return res.status(403).json({
            error: true,
            message: "You can only answer questions for your department"
          });
        }
      }

      // Calculate weight if question is critical
      let weight = 0;
      if (critical === 0) {
        weight = 0;
      } else if (question.critical === 1 || critical === 1) {
        sdk.setTable("settings");
        const settings = await sdk.get({ company_id: question.company_id });
        if (settings.length > 0) {
          const settingsData = JSON.parse(settings[0].settings);
          weight = answer === "Yes" ? settingsData.critical_weight_yes : settingsData.critical_weight_no;
        }
      }

      // Update the question
      sdk.setTable("assessment_item");
      await sdk.update(
        {
          answer,
          critical,
          weighted_value: weight,
          scored_weight: weight,
          answered_period: question.current_period,
          update_at: new Date().toISOString().slice(0, 19).replace("T", " ")
        },
        question_id
      );

      return res.status(200).json({
        error: false,
        message: "Question answered successfully",
        data: {
          question_id,
          answer,
          critical,
          period_id: question.period_id,
          current_period: question.current_period,
          department: question.department_name,
          weighted_value: weight
        }
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.put("/v3/api/custom/erpai/assessment/:assessment_id/favourites", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const { assessment_id } = req.params;
      const user_id = req.user_id; // Assuming user_id is sent in the request body
      let company_id;

      if (req.role == "company") {
        company_id = req.user_id;
      } else {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        company_id = user[0].company;
      }

      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id, company_id: company_id });
      if (assessment.length == 0) return res.status(404).json({ error: true, message: "Assessment not found" });

      // Update favourites_list
      let favouritesList = assessment[0].favourites_list || "[]";

      let parsedList = JSON.parse(favouritesList);
      if (!Array.isArray(parsedList)) {
        parsedList = []; // Fallback to an empty array if the parsed value is not an array
      }

      // Toggle user_id in the favouritesList
      if (!parsedList.includes(user_id)) {
        parsedList.push(user_id); // Add user_id if not present
      } else {
        parsedList = parsedList.filter((id) => id !== user_id); // Remove user_id if present
      }

      favouritesList = JSON.stringify(parsedList);

      await sdk.update({ favourites_list: favouritesList }, assessment_id);

      return res.status(200).json({ error: false, message: "Favourites list updated successfully" });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Create assessment
  app.post("/v3/api/custom/erpai/assessment/create", [...middlewares, TokenMiddleware({ role: "company|executive|manager" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { name, description, frequency, duration, department_ids } = req.body;
      let company_id = req.user_id;

      if (!name || !frequency || !duration) {
        return res.status(400).json({ error: true, message: "Name, frequency, and duration are required" });
      }

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      let assessmentId = await sdk.insert({
        name,
        description: description || "",
        frequency,
        duration,
        company_id,
        status: "draft",
        created_by: req.user_id
      });

      return res.status(200).json({
        error: false,
        message: "Assessment created successfully",
        assessment_id: assessmentId
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessment by ID
  app.get("/v3/api/custom/erpai/assessment/:assessment_id", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id } = req.params;
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id, company_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      // Get additional assessment data
      sdk.setTable("assessment_position");
      let positions = await sdk.get({ assessment_id });

      sdk.setTable("assessment_members");
      let members = await sdk.get({ assessment_id });

      sdk.setTable("assessment_item");
      let items = await sdk.get({ assessment_id });

      let assessmentData = {
        ...assessment[0],
        positions: positions.length,
        members: members.length,
        total_questions: items.length,
        answered_questions: items.filter((item) => item.answer && item.answer.trim() !== "").length
      };

      return res.status(200).json({ error: false, assessment: assessmentData });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Update assessment
  app.put("/v3/api/custom/erpai/assessment/:assessment_id", [...middlewares, TokenMiddleware({ role: "company|executive|manager" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id } = req.params;
      let { name, description, frequency, duration, status } = req.body;
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id, company_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      let updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (frequency !== undefined) updateData.frequency = frequency;
      if (duration !== undefined) updateData.duration = duration;
      if (status !== undefined) updateData.status = status;

      await sdk.update(updateData, assessment_id);

      return res.status(200).json({ error: false, message: "Assessment updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Add position to assessment
  app.post(
    "/v3/api/custom/erpai/assessment/:assessment_id/position",
    [...middlewares, TokenMiddleware({ role: "company|executive|manager" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { assessment_id } = req.params;
        let { name, description, sop_details } = req.body;

        if (!name) {
          return res.status(400).json({ error: true, message: "Position name is required" });
        }

        let positionId = await sdk.insert({
          assessment_id,
          name,
          description: description || "",
          sop_details: sop_details ? JSON.stringify(sop_details) : null
        });

        return res.status(200).json({
          error: false,
          message: "Position added successfully",
          position_id: positionId
        });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Add member to assessment
  app.post(
    "/v3/api/custom/erpai/assessment/:assessment_id/member",
    [...middlewares, TokenMiddleware({ role: "company|executive|manager" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { assessment_id } = req.params;
        let { user_id, position_id, name } = req.body;

        if (!user_id || !position_id) {
          return res.status(400).json({ error: true, message: "User ID and position ID are required" });
        }

        // Verify user exists
        sdk.setTable("user");
        let user = await sdk.get({ id: user_id });

        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }

        sdk.setTable("assessment_members");
        let memberId = await sdk.insert({
          assessment_id,
          user_id,
          position_id,
          name: name || user[0].name
        });

        return res.status(200).json({
          error: false,
          message: "Member added successfully",
          member_id: memberId
        });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Get assessment positions
  app.get("/v3/api/custom/erpai/assessment/:assessment_id/positions", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id } = req.params;

      sdk.setTable("assessment_position");
      let positions = await sdk.get({ assessment_id });

      // Get member count for each position
      for (let position of positions) {
        sdk.setTable("assessment_members");
        let members = await sdk.get({ assessment_id, position_id: position.id });
        position.member_count = members.length;
      }

      return res.status(200).json({ error: false, list: positions });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessment members
  app.get("/v3/api/custom/erpai/assessment/:assessment_id/members", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id } = req.params;

      sdk.setTable("assessment_members");
      let members = await sdk.get({ assessment_id });

      // Get user details and position names
      for (let member of members) {
        sdk.setTable("user");
        let user = await sdk.get({ id: member.user_id });
        if (user.length > 0) {
          member.user_details = user[0];
        }

        sdk.setTable("assessment_position");
        let position = await sdk.get({ id: member.position_id });
        if (position.length > 0) {
          member.position_name = position[0].name;
        }
      }

      return res.status(200).json({ error: false, list: members });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Start assessment
  app.post(
    "/v3/api/custom/erpai/assessment/:assessment_id/start",
    [...middlewares, TokenMiddleware({ role: "company|executive|manager" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { assessment_id } = req.params;

        sdk.setTable("assessment");
        let assessment = await sdk.get({ id: assessment_id });

        if (assessment.length === 0) {
          return res.status(404).json({ error: true, message: "Assessment not found" });
        }

        if (assessment[0].status === "active") {
          return res.status(400).json({ error: true, message: "Assessment is already active" });
        }

        await sdk.update(
          {
            status: "active",
            start_date: new Date().toISOString(),
            start_status: 1
          },
          assessment_id
        );

        return res.status(200).json({ error: false, message: "Assessment started successfully" });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Complete assessment
  app.post(
    "/v3/api/custom/erpai/assessment/:assessment_id/complete",
    [...middlewares, TokenMiddleware({ role: "company|executive|manager" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { assessment_id } = req.params;

        sdk.setTable("assessment");
        let assessment = await sdk.get({ id: assessment_id });

        if (assessment.length === 0) {
          return res.status(404).json({ error: true, message: "Assessment not found" });
        }

        await sdk.update(
          {
            status: "completed",
            end_date: new Date().toISOString()
          },
          assessment_id
        );

        return res.status(200).json({ error: false, message: "Assessment completed successfully" });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  console.log("Assessment routes module loaded");
};
