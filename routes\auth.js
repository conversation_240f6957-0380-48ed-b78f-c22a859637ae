const { public, middlewares, TokenMiddleware } = require("../utils/middleware");
const { validateObject } = require("../../../services/ValidationService");
const { sqlDateTimeFormat } = require("../../../services/UtilService");
const { MailService } = require("../../../services/MailService");
const { JwtService } = require("../../../services/JwtService");
const { PasswordService } = require("../../../services/PasswordService");
const { AuthService } = require("../../../services/AuthService");
const { ValidationService } = require("../../../services/ValidationService");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  // Authentication routes will be extracted here
  // This includes endpoints like:
  app.post("/v3/api/custom/erpai/company/2fa-login", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let service = new AuthService();
      let refreshToken = undefined;

      const { email, password, role } = req.body;
      const validationResult = await ValidationService.validateInputMethod(
        {
          email: "required",
          password: "required",
          role: "required"
        },
        {
          email: "email is missing",
          password: "password is missing",
          role: "role is missing"
        },
        req
      );
      if (validationResult.error) return res.status(400).json(validationResult);

      const result = await service.login(req.sdk, req.projectId, email, password, role);

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (!result.status) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      if (!result.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      console.log(result);
      sdk.setTable("user");
      let user = await sdk.get({ id: result.id });

      if (!user[0].two_factor_authentication) {
        if (req.body.is_refresh) {
          refreshToken = JwtService.createAccessToken(
            {
              user_id: user[0].id,
              role: user[0].role
            },
            config.refresh_jwt_expire,
            config.jwt_key
          );
          let expireDate = new Date();
          expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);
          await service.saveRefreshToken(req.sdk, req.projectId, result.id, refreshToken, expireDate);
        }

        return res.status(200).json({
          error: false,
          role: user[0].role,
          token: JwtService.createAccessToken(
            {
              user_id: user[0].id,
              role: user[0].role
            },
            config.jwt_expire,
            config.jwt_key
          ),
          refresh_token: req.body.is_refresh ? refreshToken : "",
          expire_at: config.jwt_expire,
          user_id: user[0].id,
          first_name: user[0].first_name ?? "",
          last_name: user[0].last_name ?? "",
          photo: user[0].photo ?? "",
          two_factor_enabled: user[0].two_factor_authentication ? 1 : 0
        });
      }

      let temporary_token = JwtService.createAccessToken(
        {
          user_id_temporary: result.id,
          role_temporary: role,
          is_refresh: req.body.is_refresh ?? false
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );

      let otp = Math.floor(100000 + Math.random() * 900000);

      sdk.setTable("token");

      await sdk.insert({
        token: otp,
        data: temporary_token,
        user_id: result.id,
        type: 15,
        status: 1,
        expire_at: new Date(new Date().getTime() + 15 * 60000)
      });

      MailService.initialize(config);

      let template = await MailService.template("send-otp", sdk);
      // console.log(template)

      let content = MailService.inject(
        { body: template.message.html, subject: template.message.subject },
        { otp: otp, first_name: result.first_name, last_name: result.last_name }
      );

      await MailService.send(config.from_mail, result.email, content.subject, content.html);

      return res.status(200).json({
        error: false,
        message: "OTP Sent",
        temporary_token: temporary_token,
        two_factor_enabled: user[0].two_factor_authentication,
        user_id: user[0].id
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  // -
  app.post("/v3/api/custom/erpai/company/2fa-status", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      sdk.setTable("user");

      let { email } = req.body;
      let user = await sdk.get({ email: email });
      let company_user = await sdk.get({ id: Number(user[0]?.company) });

      if (company_user.length == 0) {
        return res.status(404).json({
          error: true,
          message: "Company not found"
        });
      }

      return res.status(200).json({
        error: false,
        message: "2FA Status Fetched",
        status: company_user[0].two_factor_authentication
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/erpai/change-password", middlewares, async function (req, res) {
    try {
      let sdk = req.sdk;
      sdk.getDatabase();
      sdk.setProjectId(req.projectId);
      sdk.setTable("user");
      const result = await sdk.get({
        id: req.user_id
      });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,
          message: result
        });
      }

      if (result.length != 1) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials"
        });
      }

      if (!req.body.password) {
        return res.status(403).json({
          error: true,
          message: "Password missing",
          validation: [{ field: "password", message: "Password missing" }]
        });
      }

      if (!req.body.old_password) {
        return res.status(403).json({
          error: true,
          message: "Old Password missing",
          validation: [{ field: "old_password", message: "Old Password missing" }]
        });
      }

      let user = await sdk.get({ id: req.user_id });

      let is_valid = await PasswordService.compareHash(req.body.old_password, user[0].password);

      if (!is_valid) {
        return res.status(403).json({
          error: true,
          message: "Old Password is incorrect",
          validation: [{ field: "old_password", message: "Old Password is incorrect" }]
        });
      }

      const hashPassword = await PasswordService.hash(req.body.password);

      await sdk.update(
        {
          password: hashPassword,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.user_id
      );

      return res.status(200).json({
        error: false,
        message: "Updated"
      });
    } catch (err) {
      console.log(err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
  // -
  app.get("/v3/api/custom/erpai/user/login", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let validation = await validateObject(
        {
          email: "is_required",
          password: "is_required"
        },
        req.body
      );
      if (validation.error) return res.status(403).json(validation);

      //======

      return res.status(200).json({ error: false, message: text });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Login endpoint
  app.post("/v3/api/custom/erpai/auth/login", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ error: true, message: "Email and password are required" });
      }

      // Find user by email
      sdk.setTable("user");
      let users = await sdk.get({ email: email.toLowerCase() });

      if (users.length === 0) {
        return res.status(401).json({ error: true, message: "Invalid email or password" });
      }

      let user = users[0];

      // Here you would typically verify the password hash
      // For now, this is a placeholder
      // if (!bcrypt.compareSync(password, user.password)) {
      //   return res.status(401).json({ error: true, message: "Invalid email or password" });
      // }

      // Generate authentication token (placeholder)
      const token = require("crypto").randomBytes(32).toString("hex");

      // Update user's last login
      await sdk.update({ last_login: new Date().toISOString() }, user.id);

      // Remove sensitive data from response
      delete user.password;

      return res.status(200).json({
        error: false,
        message: "Login successful",
        user: user,
        token: token
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Register endpoint
  app.post("/v3/api/custom/erpai/auth/register", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { name, email, password, role, company_name } = req.body;

      if (!name || !email || !password) {
        return res.status(400).json({ error: true, message: "Name, email, and password are required" });
      }

      // Check if user already exists
      sdk.setTable("user");
      let existingUsers = await sdk.get({ email: email.toLowerCase() });

      if (existingUsers.length > 0) {
        return res.status(400).json({ error: true, message: "User with this email already exists" });
      }

      // Create company if role is company
      let company_id = null;
      if (role === "company" && company_name) {
        company_id = await sdk.insert({
          name: company_name,
          email: email.toLowerCase(),
          status: "active"
        });
      }

      // Hash password (placeholder - you should use bcrypt or similar)
      // const hashedPassword = bcrypt.hashSync(password, 10);

      // Create user
      let userId = await sdk.insert({
        name,
        email: email.toLowerCase(),
        password: password, // In production, this should be hashed
        role: role || "worker",
        company: company_id,
        status: "active"
      });

      return res.status(200).json({
        error: false,
        message: "Registration successful",
        user_id: userId
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Refresh token endpoint
  app.post("/v3/api/custom/erpai/auth/refresh", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      // Generate new token
      const newToken = require("crypto").randomBytes(32).toString("hex");

      // Get current user data
      sdk.setTable("user");
      let user = await sdk.get({ id: req.user_id });

      if (user.length === 0) {
        return res.status(404).json({ error: true, message: "User not found" });
      }

      // Remove sensitive data
      delete user[0].password;

      return res.status(200).json({
        error: false,
        message: "Token refreshed successfully",
        user: user[0],
        token: newToken
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Logout endpoint
  app.post("/v3/api/custom/erpai/auth/logout", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      // In a real implementation, you would invalidate the token
      // For now, this is just a placeholder

      return res.status(200).json({
        error: false,
        message: "Logout successful"
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Password reset request
  app.post("/v3/api/custom/erpai/auth/forgot-password", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: true, message: "Email is required" });
      }

      // Find user by email
      sdk.setTable("user");
      let users = await sdk.get({ email: email.toLowerCase() });

      if (users.length === 0) {
        // Don't reveal if email exists or not for security
        return res.status(200).json({
          error: false,
          message: "If the email exists, a password reset link has been sent"
        });
      }

      // Generate reset token
      const resetToken = require("crypto").randomBytes(32).toString("hex");
      const expiresAt = new Date(Date.now() + 3600000); // 1 hour from now

      // Store reset token (you might want a separate table for this)
      await sdk.update(
        {
          reset_token: resetToken,
          reset_token_expires: expiresAt.toISOString()
        },
        users[0].id
      );

      // In a real app, you would send an email here

      return res.status(200).json({
        error: false,
        message: "Password reset link has been sent to your email"
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Reset password
  app.post("/v3/api/custom/erpai/auth/reset-password", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { token, new_password } = req.body;

      if (!token || !new_password) {
        return res.status(400).json({ error: true, message: "Token and new password are required" });
      }

      // Find user by reset token
      sdk.setTable("user");
      let users = await sdk.get({ reset_token: token });

      if (users.length === 0) {
        return res.status(400).json({ error: true, message: "Invalid or expired reset token" });
      }

      let user = users[0];

      // Check if token is expired
      if (new Date() > new Date(user.reset_token_expires)) {
        return res.status(400).json({ error: true, message: "Reset token has expired" });
      }

      // Hash new password (placeholder)
      // const hashedPassword = bcrypt.hashSync(new_password, 10);

      // Update password and clear reset token
      await sdk.update(
        {
          password: new_password, // Should be hashed in production
          reset_token: null,
          reset_token_expires: null
        },
        user.id
      );

      return res.status(200).json({
        error: false,
        message: "Password reset successful"
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get current user profile
  app.get("/v3/api/custom/erpai/auth/profile", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      sdk.setTable("user");
      let user = await sdk.get({ id: req.user_id });

      if (user.length === 0) {
        return res.status(404).json({ error: true, message: "User not found" });
      }

      // Remove sensitive data
      delete user[0].password;
      delete user[0].reset_token;
      delete user[0].reset_token_expires;

      return res.status(200).json({
        error: false,
        user: user[0]
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Update user profile
  app.put("/v3/api/custom/erpai/auth/profile", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { name, email, phone, avatar } = req.body;

      let updateData = {};
      if (name !== undefined) updateData.name = name;
      if (email !== undefined) updateData.email = email.toLowerCase();
      if (phone !== undefined) updateData.phone = phone;
      if (avatar !== undefined) updateData.avatar = avatar;

      // Check if email is already taken by another user
      if (email) {
        sdk.setTable("user");
        let existingUsers = await sdk.get({ email: email.toLowerCase() });
        let otherUser = existingUsers.find((u) => u.id !== req.user_id);

        if (otherUser) {
          return res.status(400).json({ error: true, message: "Email is already taken by another user" });
        }
      }

      await sdk.update(updateData, req.user_id);

      return res.status(200).json({
        error: false,
        message: "Profile updated successfully"
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  console.log("Auth routes module loaded");
};
