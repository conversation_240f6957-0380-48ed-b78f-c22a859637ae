const { middlewares, public, TokenMiddleware } = require("../utils/middleware");
const { errorCatcher } = require("../utils/errorHandler");
const { ds_server_base_url, ds_server_api_key } = require("../utils/constants");
const {
  getDepartments,
  calculateProblematicAreas,
  calculateQuestionStats,
  calculateUserStats,
  calculateUserStatsByAssessment,
  getDataForAI,
  getAssessmentDetailsV2
} = require("../service/calculations");
const { sqlDateTimeFormat } = require("../../../services/UtilService");
const { default: axios } = require("axios");

module.exports = function (app) {
  // Get departments for all user types
  app.get("/v3/api/custom/erpai/common/departments", [...middlewares], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: company_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      let departments = await getDepartments(sdk, company_id);

      return res.status(200).json({ error: false, list: departments });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Finish setup endpoint
  app.get("/v3/api/custom/erpai/common/finish-setup/:assessment_id", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id } = req.params;

      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      sdk.setTable("assessment_position");
      let positions = await sdk.get({ assessment_id: assessment_id });
      let sops = [];

      for (let position of positions) {
        sops.push({
          role: position.name,
          role_id: position.id,
          sops: position.sop_details ? JSON.parse(position.sop_details) : {},
          area_tags: [],
          members: []
        });

        sdk.setTable("assessment_area");
        let areas = await sdk.get({ assessment_id: assessment_id, position_id: position.id });
        for (let area of areas) {
          sops[sops.length - 1].area_tags.push({ name: area.name, area_id: area.id });
        }

        sdk.setTable("assessment_members");
        let members = await sdk.get({ assessment_id: assessment_id, position_id: position.id });
        for (let member of members) {
          sdk.setTable("user");
          let user = await sdk.get({ id: member.user_id });

          if (user.length > 0) {
            sops[sops.length - 1].members.push({
              name: member.name,
              id: user[0].id
            });
          }
        }
      }

      let payload = {
        sops: sops,
        assessment_type: assessment[0].frequency,
        duration: assessment[0].duration
      };

      let response = await axios.post(`${ds_server_base_url}/api/v1/qs/generate_questions_from_sop-latest`, payload, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${ds_server_api_key}`
        }
      });

      let items = [];

      // Generate items for each period
      for (let j = 0; j < assessment[0].duration; j++) {
        for (let i = 0; i < response.data.questions.questions.length; i++) {
          let entity = response.data.questions.questions[i];

          sdk.setTable("assessment_members");
          let members = await sdk.get({ assessment_id: assessment_id, user_id: entity.assigned_to });

          // Create new area if needed
          if (!entity.area_tag) {
            sdk.setTable("assessment_area");
            const area = await sdk.get({ assessment_id, name: entity.area_name, position_id: members[0].position_id });
            if (area.length > 0) {
              entity.area_tag = area[0].id;
            } else {
              entity.area_tag = await sdk.insert({ assessment_id, name: entity.area_name, position_id: members[0].position_id });
            }
          }

          items.push({
            question: entity.questions,
            assigned_to: entity.assigned_to,
            area_id: entity.area_tag,
            critical: 0,
            assessment_id,
            period_id: j + 1
          });
        }
      }

      // Insert all items
      let insertPromises = [];
      for (let item of items) {
        sdk.setTable("assessment_item");
        insertPromises.push(sdk.insert(item));
      }

      await Promise.all(insertPromises);

      // Update assessment status
      sdk.setTable("assessment");
      await sdk.update({ start_status: 1, start_date: sqlDateTimeFormat(new Date()) }, assessment_id);

      return res.status(200).json({ error: false, message: "Setup completed successfully", items_created: items.length });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Assessment settings
  app.post("/v3/api/custom/erpai/common/assesment-settings/", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { duration, assessment_id, frequency } = req.body;

      if (!assessment_id || !duration || !frequency) {
        return res.status(400).json({ error: true, message: "Missing required parameters" });
      }

      sdk.setTable("assessment");
      await sdk.update({ duration, frequency }, assessment_id);

      return res.status(200).json({ error: false, message: "Settings updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessment settings hint
  app.get("/v3/api/custom/erpai/common/get-assessment-settings-hint/:assessment_id", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id } = req.params;

      sdk.setTable("assessment_position");
      let positions = await sdk.get({ assessment_id: assessment_id });

      let role_sops = [];

      for (let position of positions) {
        role_sops.push({
          role: position.name,
          sops: position.sop_details ? JSON.parse(position.sop_details) : {}
        });
      }

      let response = await axios.post(
        `${ds_server_base_url}/api/v1/bot/suggest_assessment_frequencies`,
        {
          sops: {
            role_sops: role_sops
          },
          options: ["weekly", "monthly", "yearly"]
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${ds_server_api_key}`
          }
        }
      );

      return res.status(200).json({
        error: false,
        message: "Success",
        model: {
          frequency: response.data.response["assessment_type"],
          duration: response.data.response["duration"],
          justifications: response.data.response["Justification"]
        }
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get areas suggestions
  app.post("/v3/api/custom/erpai/common/get-areas", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { position_id, existing_areas } = req.body;

      if (!existing_areas || existing_areas.length < 3) {
        return res.status(400).json({ error: true, message: "At least 3 areas are required" });
      }

      sdk.setTable("sop_position");
      let position = await sdk.get({ id: position_id });

      if (position.length === 0) {
        return res.status(404).json({ error: true, message: "Position not found" });
      }

      let payload = {
        position: position[0].name,
        existing_areas: existing_areas
      };

      let response = await axios.post(`${ds_server_base_url}/api/v1/bot/suggest_more_areas`, payload, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${ds_server_api_key}`
        }
      });

      let areas = response.data.areas.areas;

      return res.status(200).json({ error: false, list: [...existing_areas, ...areas] });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Save areas
  app.post("/v3/api/custom/erpai/common/save-areas", [...middlewares, TokenMiddleware()], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      const { areas, position_id, sop_id } = req.body;

      if (!areas || !Array.isArray(areas) || areas.length < 5) {
        return res.status(400).json({ error: true, message: "At least 5 areas are required" });
      }

      if (!position_id) {
        return res.status(400).json({ error: true, message: "Position ID is required" });
      }

      sdk.setTable("sop_position");
      let position = await sdk.get({ id: position_id });

      if (position.length === 0) {
        return res.status(404).json({ error: true, message: "Position not found" });
      }

      sdk.setTable("assessment_area");
      for (let area of areas) {
        await sdk.insert({ name: area, position_id, sop_id });
      }

      return res.status(200).json({ error: false, message: "Areas saved successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Calculate problematic areas
  app.get(
    "/v3/api/custom/erpai/common/calculate-problematic-areas",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let company_id = req.user_id;

        if (req.role !== "company") {
          sdk.setTable("user");
          let user = await sdk.get({ id: req.user_id });
          if (user.length === 0) {
            return res.status(404).json({ error: true, message: "User not found" });
          }
          company_id = user[0].company;
        }

        let { date = new Date().toISOString().split("T")[0] } = req.query;

        let areas = await calculateProblematicAreas(sdk, company_id, date);

        return res.status(200).json({ error: false, list: areas });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Calculate unanswered questions
  app.get(
    "/v3/api/custom/erpai/common/calculate-unanswered-questions",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let company_id = req.user_id;

        if (req.role !== "company") {
          sdk.setTable("user");
          let user = await sdk.get({ id: req.user_id });
          if (user.length === 0) {
            return res.status(404).json({ error: true, message: "User not found" });
          }
          company_id = user[0].company;
        }

        const { date = new Date().toISOString().split("T")[0], limit = 10, page = 1 } = req.query;

        const numericLimit = parseInt(limit, 10);
        const numericPage = parseInt(page, 10);

        if (isNaN(numericLimit) || isNaN(numericPage) || numericLimit <= 0 || numericPage <= 0) {
          return res.status(400).json({ error: true, message: "Invalid limit or page parameters" });
        }

        const allQuestions = await calculateQuestionStats(sdk, company_id, date);
        const total = allQuestions.length;
        const offset = (numericPage - 1) * numericLimit;
        const paginatedQuestions = allQuestions.slice(offset, offset + numericLimit);

        return res.status(200).json({
          error: false,
          list: paginatedQuestions,
          total,
          limit: numericLimit,
          page: numericPage,
          num_pages: Math.ceil(total / numericLimit)
        });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Calculate user stats
  app.get(
    "/v3/api/custom/erpai/common/calculate-user-stats",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let user_id = req.user_id;
        let company_id = "";

        if (req.role === "company") {
          company_id = req.user_id;
        } else {
          sdk.setTable("user");
          let user = await sdk.get({ id: user_id });
          if (user.length === 0) {
            return res.status(404).json({ error: true, message: "User not found" });
          }
          company_id = user[0].company;
        }

        let { date = new Date().toISOString().split("T")[0] } = req.query;

        let users = await calculateUserStats(sdk, company_id, date);

        return res.status(200).json({ error: false, list: users });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Calculate user stats per assessment
  app.get(
    "/v3/api/custom/erpai/common/calculate-user-stats-per-assessment",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let company_id = req.user_id;
        let { date = new Date().toISOString().split("T")[0] } = req.query;

        let users = await calculateUserStatsByAssessment(sdk, company_id, null, date);

        return res.status(200).json({ error: false, list: users });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Get data for AI
  app.post("/v3/api/custom/erpai/common/get-data-ai", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { type, options } = req.body;

      if (!type) {
        return res.status(400).json({ error: true, message: "Type parameter is required" });
      }

      let data = await getDataForAI(sdk, type, options);

      return res.status(200).json({ error: false, data: data });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessment details
  app.get(
    "/v3/api/custom/erpai/common/get-assessment-details/:assessment_id",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker|viewer" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let { assessment_id } = req.params;
        let data = await getAssessmentDetailsV2(sdk, assessment_id);
        return res.status(200).json({ error: false, data: data });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Get settings
  app.get(
    "/v3/api/custom/erpai/common/get-settings",
    [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker|viewer" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let company_id = req.user_id;

        if (req.role !== "company") {
          sdk.setTable("user");
          let user = await sdk.get({ id: req.user_id });
          if (user.length === 0) {
            return res.status(404).json({ error: true, message: "User not found" });
          }
          company_id = user[0].company;
        }

        sdk.setTable("settings");
        let settings = await sdk.get({ company_id: company_id });

        if (settings.length === 0) {
          let default_settings = {
            overall_result_pass: 90,
            overall_result_fail: 90,
            area_health_green: 90,
            area_health_yellow: 75,
            critical_weight_yes: 25,
            critical_weight_no: 5,
            area_health_red: 75
          };

          await sdk.insert({ company_id: company_id, settings: JSON.stringify(default_settings) });
          return res.status(200).json({ error: false, settings: default_settings });
        } else {
          return res.status(200).json({ error: false, settings: JSON.parse(settings[0].settings) });
        }
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Get bar graph data
  app.post("/v3/api/custom/erpai/common/get-bar-graph-data", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id, area_id } = req.body;

      if (!assessment_id) {
        return res.status(400).json({ error: true, message: "Assessment ID is required" });
      }

      // TODO: Implement bar graph data calculation logic
      return res.status(200).json({ error: false, data: [] });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get pie chart data
  app.post("/v3/api/custom/erpai/common/get-pie-chart-data", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id, area_id } = req.body;

      if (!assessment_id) {
        return res.status(400).json({ error: true, message: "Assessment ID is required" });
      }

      // TODO: Implement pie chart data calculation logic
      return res.status(200).json({ error: false, data: [] });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessments
  app.post("/v3/api/custom/erpai/common/get-assessments", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("assessment");
      let assessments = await sdk.get({ company_id });

      return res.status(200).json({ error: false, list: assessments });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessments overview
  app.get("/v3/api/custom/erpai/common/get-assessments-overview", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("assessment");
      let assessments = await sdk.get({ company_id });

      // Add summary information for each assessment
      for (let assessment of assessments) {
        sdk.setTable("assessment_item");
        let items = await sdk.get({ assessment_id: assessment.id });
        assessment.total_questions = items.length;
        assessment.answered_questions = items.filter((item) => item.answer && item.answer.trim() !== "").length;
        assessment.completion_percentage = assessment.total_questions > 0 ? Math.round((assessment.answered_questions / assessment.total_questions) * 100) : 0;
      }

      return res.status(200).json({ error: false, list: assessments });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Update answer
  app.post("/v3/api/custom/erpai/common/update-answer", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { question_id, answer } = req.body;

      if (!question_id || answer === undefined) {
        return res.status(400).json({ error: true, message: "Question ID and answer are required" });
      }

      sdk.setTable("assessment_item");
      await sdk.update({ answer }, question_id);

      return res.status(200).json({ error: false, message: "Answer updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get assessment item
  app.get("/v3/api/custom/erpai/common/get-assessment-item", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { item_id } = req.query;

      if (!item_id) {
        return res.status(400).json({ error: true, message: "Item ID is required" });
      }

      sdk.setTable("assessment_item");
      let item = await sdk.get({ id: item_id });

      return res.status(200).json({ error: false, item: item[0] || null });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Validate item document
  app.post("/v3/api/custom/erpai/common/validate-item-document", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { document_url, question } = req.body;

      if (!document_url || !question) {
        return res.status(400).json({ error: true, message: "Document URL and question are required" });
      }

      // TODO: Implement document validation logic
      return res.status(200).json({
        error: false,
        validation: {
          is_valid: true,
          score: 85,
          feedback: "Document validation not yet implemented"
        }
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Direct prompt bot
  app.post("/v3/api/custom/erpai/common/direct-prompt-bot", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { prompt, context } = req.body;

      if (!prompt) {
        return res.status(400).json({ error: true, message: "Prompt is required" });
      }

      let response = await axios.post(
        `${ds_server_base_url}/api/v1/bot/direct_prompt`,
        {
          prompt,
          context: context || {}
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${ds_server_api_key}`
          }
        }
      );

      return res.status(200).json({ error: false, response: response.data });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Predict next assessments
  app.get("/v3/api/custom/erpai/common/predict-next-n-assessments", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let company_id = req.user_id;
      let { n = 5 } = req.query;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      const predictionCount = parseInt(n, 10);
      if (isNaN(predictionCount) || predictionCount <= 0) {
        return res.status(400).json({ error: true, message: "Invalid prediction count" });
      }

      // TODO: Implement prediction logic
      return res.status(200).json({
        error: false,
        predictions: []
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });
};
