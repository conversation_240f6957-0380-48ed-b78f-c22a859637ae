const { middlewares, public, TokenMiddleware } = require("../utils/middleware");
const { checkSubscriptionLimits } = require("../service/subscriptionService");
const { validateSettings } = require("../service/calculations");
const { sqlDateTimeFormat } = require("../../../services/UtilService");
const { default: axios } = require("axios");
const { AuthService } = require("../../../services/AuthService");
const { MailService } = require("../../../services/MailService");
const config = require("../../../config");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  app.post("/v3/api/custom/erpai/company/save-settings", [...middlewares, TokenMiddleware({ role: "executive|company|manager|worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { settings } = req.body;
      let company_id = req.user_id;

      if (!settings) {
        return res.status(400).json({ error: true, message: "Settings are required" });
      }

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("settings");
      let existingSettings = await sdk.get({ company_id });

      if (existingSettings.length === 0) {
        await sdk.insert({ company_id, settings: JSON.stringify(settings) });
      } else {
        await sdk.update({ settings: JSON.stringify(settings) }, existingSettings[0].id);
      }

      return res.status(200).json({ error: false, message: "Settings saved successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  app.post("/v3/api/custom/erpai/company/add-team-member", [...middlewares, TokenMiddleware({ role: "company|executive" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let newUser = {
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        email: req.body.email,
        password: req.body.password,
        role: req.body.role,
        position: req.body.position,
        position_id: req.body.position_id ?? null
      };
      sdk.setTable("user");
      let existing_user = await sdk.get({ email: newUser.email });
      if (existing_user.length > 0) {
        return res.status(400).json({ error: true, message: "User with this email already exists" });
      }

      const current_user_id = req.user_id;
      const current_role = req.role;

      let company_id;

      if (current_role === "company") {
        company_id = current_user_id;
      } else {
        sdk.setTable("user");
        const user = await sdk.get({ id: current_user_id });
        company_id = user[0].company;
      }

      // Add subscription limit check
      sdk.setTable("user");
      // const existingTeamMembers = await sdk.get({ company: company_id, status: 1 });
      const existingTeamMembers = await sdk.rawQuery(`SELECT * FROM erpai_user WHERE company = ${company_id} AND status = 1 AND role != "viewer"`);

      const limitCheck = await checkSubscriptionLimits(sdk, company_id, "user", existingTeamMembers.length + 1);
      if (!limitCheck.allowed) {
        return res.status(403).json({
          error: true,
          message: limitCheck.message
        });
      }

      let authService = new AuthService();
      let actual_role = "";

      if (newUser.role == "worker" || newUser.role == "user") {
        actual_role = "worker";
      }

      if (newUser.role == "srp" || newUser.role == "prp" || newUser.role == "manager") {
        actual_role = "manager";
      }

      if (newUser.role == "executive") {
        actual_role = "executive";
      }

      let user_id = await authService.register(sdk, req.projectId, newUser.email, newUser.password, actual_role, 1, newUser.first_name, newUser.last_name);

      sdk.setTable("user");

      await sdk.update({ role_slug: newUser.role, position: newUser.position, position_id: newUser.position_id, company: req.user_id }, user_id);

      MailService.initialize(config);

      let template = await MailService.template("add_team_member", sdk);
      console.log(template);

      let content = MailService.inject(
        { body: template.message.html, subject: template.message.subject },
        { first_name: newUser.first_name, last_name: newUser.last_name, email: newUser.email, password: newUser.password, role: actual_role }
      );

      await MailService.send(config.from_mail, newUser.email, content.subject, content.html);

      return res.status(200).json({ error: false, message: "Invited User Successfully" });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v3/api/custom/erpai/company/create-assessment", [...middlewares, TokenMiddleware({ role: "company|executive|manager" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const {
        name,
        selected_sop = null, // ID of the selected SOP
        department_id = null,
        role_slug = "worker",
        frequency = null,
        duration = null,
        start_date = null,
        questions = null // Optional array of edited questions
      } = req.body;

      let company_id = req.user_id;
      if (req.role == "company") {
        company_id = req.user_id;
      } else {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        company_id = user[0].company;
      }

      // Check assessment limit
      sdk.setTable("assessment");
      const existingAssessments = await sdk.get({ company_id });
      const limitCheck = await checkSubscriptionLimits(sdk, company_id, "assessment", existingAssessments.length + 1);
      if (!limitCheck.allowed) {
        return res.status(403).json({
          error: true,
          message: limitCheck.message
        });
      }

      // Create assessment data
      const assessmentData = {
        name,
        company_id,
        department_id,
        role_slug,
        frequency,
        duration,
        start_date: start_date ? sqlDateTimeFormat(new Date(start_date)) : null,
        creator_id: req.user_id,
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
        start_status: 0
      };

      // If SOP is selected, verify it exists and belongs to company
      if (selected_sop) {
        sdk.setTable("sop");
        const sop = await sdk.get({ id: selected_sop, company_id });
        if (sop.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Selected SOP not found or unauthorized"
          });
        }

        // Add SOP reference to assessment
        assessmentData.sop_id = selected_sop;

        // Create assessment first
        sdk.setTable("assessment");
        const assessment_id = await sdk.insert(assessmentData);

        // If questions are provided, use them directly
        if (questions) {
          // Validate questions array
          if (!Array.isArray(questions)) {
            return res.status(400).json({
              error: true,
              message: "Questions must be an array"
            });
          }

          // Process and insert the edited questions
          const processedQuestions = questions.map((question) => ({
            assessment_id,
            sop_id: selected_sop,
            question: question.question,
            area_id: question.area_id,
            assigned_to: question.assigned_to,
            critical: question.critical || 0,
            period_id: 1,
            create_at: sqlDateTimeFormat(new Date())
          }));

          // Insert questions in batches
          const batchSize = 50;
          for (let i = 0; i < processedQuestions.length; i += batchSize) {
            const batch = processedQuestions.slice(i, i + batchSize);
            await Promise.all(
              batch.map((question) => {
                sdk.setTable("assessment_item");
                return sdk.insert(question);
              })
            );
          }

          return res.status(200).json({
            error: false,
            message: "Assessment created successfully with edited questions",
            data: {
              assessment_id,
              name,
              sop_id: selected_sop,
              question_count: processedQuestions.length,
              department_id,
              role_slug,
              frequency,
              duration,
              start_date
            }
          });
        } else {
          // Generate fresh questions from SOP
          const sopDetails = JSON.parse(sop[0].sop_details);
          const sopType = sop[0].type;

          // Create form data for question generation
          const formData = new FormData();
          formData.append("sop_details", JSON.stringify(sopDetails));
          formData.append("sop_type", sopType);
          formData.append("assessment_id", assessment_id);

          // Generate questions using DS server
          const response = await axios.post(`${ds_server_base_url}/api/v1/sop/generate_questions`, formData, {
            headers: {
              ...formData.getHeaders(),
              Authorization: `Bearer ${ds_server_api_key}`
            }
          });

          // Return the generated questions without saving them
          const generatedQuestions = response.data.questions.map((question) => ({
            question: question.question,
            area_id: question.area_id,
            assigned_to: question.assigned_to,
            critical: question.critical || 0
          }));

          return res.status(200).json({
            error: false,
            message: "Assessment created with generated questions",
            data: {
              assessment_id,
              name,
              sop_id: selected_sop,
              department_id,
              role_slug,
              frequency,
              duration,
              start_date,
              questions: generatedQuestions // Return questions for editing
            }
          });
        }
      } else {
        // Create assessment without SOP
        sdk.setTable("assessment");
        const assessment_id = await sdk.insert(assessmentData);

        return res.status(200).json({
          error: false,
          message: "Assessment created successfully",
          data: {
            assessment_id,
            name,
            department_id,
            role_slug,
            frequency,
            duration,
            start_date
          }
        });
      }
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Delete assessment
  app.delete(
    "/v3/api/custom/erpai/company/delete-assessment/:assessment_id",
    [...middlewares, TokenMiddleware({ role: "company|executive" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);
      try {
        let { assessment_id } = req.params;
        let company_id = req.user_id;

        if (req.role !== "company") {
          sdk.setTable("user");
          let user = await sdk.get({ id: req.user_id });
          if (user.length === 0) {
            return res.status(404).json({ error: true, message: "User not found" });
          }
          company_id = user[0].company;
        }

        // Verify assessment belongs to company
        sdk.setTable("assessment");
        let assessment = await sdk.get({ id: assessment_id, company_id: company_id });
        if (assessment.length === 0) {
          return res.status(404).json({ error: true, message: "Assessment not found" });
        }

        // Delete related data in proper order
        sdk.setTable("assessment_item");
        await sdk.deleteWhere({ assessment_id });

        sdk.setTable("assessment_area");
        await sdk.deleteWhere({ assessment_id });

        sdk.setTable("assessment_members");
        await sdk.deleteWhere({ assessment_id });

        sdk.setTable("assessment_position");
        await sdk.deleteWhere({ assessment_id });

        sdk.setTable("assessment");
        await sdk.deleteWhere({ id: assessment_id });

        return res.status(200).json({ error: false, message: "Assessment deleted successfully" });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // Department selection
  app.post("/v3/api/custom/erpai/company/department-selection", [...middlewares, TokenMiddleware({ role: "company" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { departments } = req.body;
      let company_id = req.user_id;

      if (!departments || !Array.isArray(departments)) {
        return res.status(400).json({ error: true, message: "Departments array is required" });
      }

      // Check subscription limits
      const limitCheck = await checkSubscriptionLimits(sdk, company_id, "department", departments.length);
      if (!limitCheck.allowed) {
        return res.status(403).json({ error: true, message: limitCheck.message });
      }

      // Process departments
      for (let dept of departments) {
        sdk.setTable("department");
        let existing = await sdk.get({ name: dept.name, company_id });

        if (existing.length === 0) {
          await sdk.insert({ name: dept.name, company_id, default_id: dept.default_id || null });
        }
      }

      return res.status(200).json({ error: false, message: "Departments selected successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Edit departments
  app.post("/v3/api/custom/erpai/company/edit-departments", [...middlewares, TokenMiddleware({ role: "company" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { departments } = req.body;
      let company_id = req.user_id;

      if (!departments || !Array.isArray(departments)) {
        return res.status(400).json({ error: true, message: "Departments array is required" });
      }

      for (let dept of departments) {
        if (dept.id) {
          sdk.setTable("department");
          await sdk.update({ name: dept.name }, dept.id);
        }
      }

      return res.status(200).json({ error: false, message: "Departments updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Update team member
  app.post("/v3/api/custom/erpai/company/update-team-member/:user_id", [...middlewares, TokenMiddleware({ role: "company|executive" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { user_id } = req.params;
      let { name, email, role, department_id, position, status } = req.body;
      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      // Verify user belongs to company
      sdk.setTable("user");
      let user = await sdk.get({ id: user_id, company: company_id });

      if (user.length === 0) {
        return res.status(404).json({ error: true, message: "Team member not found" });
      }

      // Update user
      let updateData = {};
      if (name !== undefined) updateData.name = name;
      if (email !== undefined) updateData.email = email;
      if (role !== undefined) updateData.role = role;
      if (department_id !== undefined) updateData.department_id = department_id;
      if (position !== undefined) updateData.position = position;
      if (status !== undefined) updateData.status = status;

      await sdk.update(updateData, user_id);

      return res.status(200).json({ error: false, message: "Team member updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Add department
  app.post("/v3/api/custom/erpai/company/add-department", [...middlewares, TokenMiddleware({ role: "company" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { name, description } = req.body;
      let company_id = req.user_id;

      if (!name) {
        return res.status(400).json({ error: true, message: "Department name is required" });
      }

      // Check if department already exists
      sdk.setTable("department");
      let existing = await sdk.get({ name, company_id });

      if (existing.length > 0) {
        return res.status(400).json({ error: true, message: "Department with this name already exists" });
      }

      // Check subscription limits
      let currentDepartments = await sdk.get({ company_id });
      const limitCheck = await checkSubscriptionLimits(sdk, company_id, "department", currentDepartments.length + 1);

      if (!limitCheck.allowed) {
        return res.status(403).json({ error: true, message: limitCheck.message });
      }

      // Create department
      let departmentId = await sdk.insert({
        name,
        description: description || "",
        company_id
      });

      return res.status(200).json({
        error: false,
        message: "Department added successfully",
        department_id: departmentId
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get packages
  app.get("/v3/api/custom/erpai/company/get-pacakeges", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      sdk.setTable("stripe_price_details");
      let packages = await sdk.get({});

      return res.status(200).json({ error: false, list: packages });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Update favorite assessment
  app.put("/v3/api/custom/erpai/company/update-favorite", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { assessment_id, is_favorite } = req.body;
      let company_id = req.user_id;

      if (!assessment_id || is_favorite === undefined) {
        return res.status(400).json({ error: true, message: "Assessment ID and favorite status are required" });
      }

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      // Verify assessment belongs to company
      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id, company_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      // Update favorite status
      await sdk.update({ is_favorite: is_favorite ? 1 : 0 }, assessment_id);

      return res.status(200).json({ error: false, message: "Favorite status updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  console.log("Company routes module loaded");
};
