const adminRoutes = require("./admin");
const companyRoutes = require("./company");
const workerRoutes = require("./worker");
const viewerRoutes = require("./viewer");
const commonRoutes = require("./common");
const assessmentRoutes = require("./assessment");
const sopRoutes = require("./sop");
const authRoutes = require("./auth");

// Keep existing subscription routes if they exist
let subscriptionRoutes;
try {
  subscriptionRoutes = require("./subscription");
} catch (err) {
  console.log("No subscription routes found, skipping...");
}

module.exports = function (app) {
  // Initialize all route modules
  console.log("🚀 Initializing ERP AI Routes...");

  // Core business routes
  adminRoutes(app);
  companyRoutes(app);
  workerRoutes(app);
  viewerRoutes(app);

  // Shared functionality
  commonRoutes(app);
  assessmentRoutes(app);
  sopRoutes(app);
  authRoutes(app);

  // Legacy subscription routes (if they exist)
  if (subscriptionRoutes) {
    subscriptionRoutes(app);
  }

  console.log("✅ All routes initialized successfully");
};
