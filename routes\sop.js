const { middlewares, TokenMiddleware } = require("../utils/middleware");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  // Use existing SOP
  app.post("/v3/api/custom/erpai/common/use-existing-sop", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { sop_id, assessment_id } = req.body;

      if (!sop_id || !assessment_id) {
        return res.status(400).json({ error: true, message: "SOP ID and Assessment ID are required" });
      }

      let company_id = req.user_id;

      if (req.role !== "company") {
        sdk.setTable("user");
        let user = await sdk.get({ id: req.user_id });
        if (user.length === 0) {
          return res.status(404).json({ error: true, message: "User not found" });
        }
        company_id = user[0].company;
      }

      sdk.setTable("sop");
      let sop = await sdk.get({ id: sop_id });

      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id, company_id: company_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      if (sop.length === 0) {
        return res.status(404).json({ error: true, message: "SOP not found" });
      }

      // Update assessment with appropriate SOP
      if (sop[0].type === "executive") {
        await sdk.update({ executive_sop: sop_id }, assessment_id);
      } else if (sop[0].type === "manager") {
        await sdk.update({ manager_sop: sop_id }, assessment_id);
      } else if (sop[0].type === "worker") {
        await sdk.update({ worker_sop: sop_id }, assessment_id);
      } else {
        return res.status(400).json({ error: true, message: "Invalid SOP type" });
      }

      return res.status(200).json({ error: false, message: "SOP assigned successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Edit whole SOP
  app.post("/v3/api/custom/erpai/common/edit-whole-sop", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { sop_id, sop_details } = req.body;

      if (!sop_id || !sop_details) {
        return res.status(400).json({ error: true, message: "SOP ID and details are required" });
      }

      sdk.setTable("sop");
      await sdk.update({ sop_details: JSON.stringify(sop_details) }, sop_id);

      return res.status(200).json({ error: false, message: "SOP updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Edit SOP
  app.post("/v3/api/custom/erpai/common/edit-sop", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { sop_details, position_id } = req.body;

      if (!sop_details || !position_id) {
        return res.status(400).json({ error: true, message: "SOP details and position ID are required" });
      }

      sdk.setTable("assessment_position");
      await sdk.update({ sop_details: JSON.stringify(sop_details) }, position_id);

      return res.status(200).json({ error: false, message: "Position SOP updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Edit SOP bulk
  app.post("/v3/api/custom/erpai/common/edit-sop-bulk", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { data } = req.body;

      if (!data || !Array.isArray(data)) {
        return res.status(400).json({ error: true, message: "Data array is required" });
      }

      for (let item of data) {
        let { sop_details, position_id } = item;

        if (sop_details && position_id) {
          sdk.setTable("assessment_position");
          await sdk.update({ sop_details: JSON.stringify(sop_details) }, position_id);
        }
      }

      return res.status(200).json({ error: false, message: "Bulk SOP update completed successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Edit SOP questions
  app.post("/v3/api/custom/erpai/common/edit-sop-questions", [...middlewares, TokenMiddleware({ role: "executive|company|manager" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { sop_questions, sop_id } = req.body;

      if (!sop_questions || !sop_id) {
        return res.status(400).json({ error: true, message: "SOP questions and SOP ID are required" });
      }

      sdk.setTable("sop");
      await sdk.update({ sop_questions: JSON.stringify(sop_questions) }, sop_id);

      return res.status(200).json({ error: false, message: "SOP questions updated successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  console.log("SOP routes module loaded");
};
