const JwtService = require("../../../services/JwtService");
const StripeService = require("../../../services/StripeService");
const ValidationService = require("../../../services/ValidationService");
const AuthService = require("../../../services/AuthService");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");

const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const SyncStripeWebhook = require("../../../middleware/SyncStripeWebhook");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  SyncStripeWebhook
  // RateLimitMiddleware,
  // LogMiddleware,
  // UsageMiddleware
  // CheckProjectMiddleware,
  // AnalyticMiddleware,
  // RoleMiddleware
];

module.exports = function (app) {
  const stripe = new StripeService();
  function jsonExtractor(object, property) {
    return `json_unquote(json_extract(${object}, '$.${property}'))`;
  }

  app.post("/v3/api/custom/erpai/stripe/customer/subscription", middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      const { planId } = req.body;
      const validationResult = await ValidationService.validateObject(
        {
          planId: "required"
        },
        { planId }
      );

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }

      sdk.setProjectId(req.projectId);

      const db = sdk.getDatabase();
      const userId = req.user_id;
      const userTable = `${sdk.getProjectId()}_user`;
      const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
      const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
      const subHistoryTable = `${sdk.getProjectId()}_subscription_history`;

      const customer = await sdk.rawQuery(`
            SELECT u.*, s.id as subId, s.stripe_id as subStripeId,
            ${jsonExtractor("s.object", "items.data[0].id")} as subItemId, p.id AS planId, p.type as planType
            FROM ${userTable} AS u LEFT JOIN ${stripeSubTable} AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') 
            LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
            WHERE u.id = ${userId} ;
        `);

      if (!customer[0]) {
        return res.status(404).json({ error: true, message: "Customer not found" });
      }

      if (customer[0].subId) {
        return res.status(401).json({ error: true, message: "Customer already has an active subscription" });
      }

      if (customer[0].stripe_uid === null) return res.status(404).json({ error: true, message: "Customer Stripe ID not found, please add a card first." });

      const stripeCustomer = await stripe.retrieveStripeCustomer({ customerId: customer[0].stripe_uid });
      console.log(stripeCustomer);
      if (!stripeCustomer.default_source && !stripeCustomer.sources?.data?.length && !stripeCustomer.invoice_settings?.default_payment_method) {
        return res.status(403).json({ error: true, message: "You don't have a valid card attached, please add one and try again" });
      }

      sdk.setTable("stripe_price");
      const metadata = {
        projectId: sdk.getProjectId()
      };

      const plan = await sdk.get({ id: planId });
      if (!plan[0]) {
        return res.status(404).json({ error: true, message: "Plan not found" });
      }

      const subscription = await stripe.createStripeSubscription({
        customerId: customer[0].stripe_uid,
        priceId: plan[0].stripe_id,
        default_payment_method: stripeCustomer.default_source || stripeCustomer.sources?.data[0]?.id || stripeCustomer.invoice_settings?.default_payment_method,
        trial_from_plan: true,
        metadata
      });

      // Record subscription history
      sdk.setTable("subscription_history");
      await sdk.insert({
        user_id: userId,
        subscription_id: subscription.id,
        price_id: plan[0].id,
        action: "created",
        status: subscription.status,
        details: JSON.stringify({
          plan_name: plan[0].name || plan[0].nickname || "Unknown Plan",
          amount: plan[0].amount || 0,
          currency: "usd",
          interval: plan[0].recurring?.interval || "month"
        }),
        create_at: new Date()
      });

      // await stripe.updateInvoice(subscription.latest_invoice, { projectId: req.projectId });
      res.status(200).json({ error: false, message: "User subscribed successfully" });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });

  // Add API to cancel subscription
  app.post("/v3/api/custom/erpai/stripe/customer/subscription/cancel", middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      const userId = req.user_id;
      const userTable = `${sdk.getProjectId()}_user`;
      const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
      const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
      const subHistoryTable = `${sdk.getProjectId()}_subscription_history`;

      // Get active subscription
      const subscription = await sdk.rawQuery(`
    SELECT s.*, p.id AS planId, p.name as planName, p.amount,
    ${jsonExtractor("p.object", "recurring.interval")} as \`interval\`
    FROM ${stripeSubTable} AS s 
    LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
    WHERE s.user_id = ${userId} AND (s.status = 'active' OR s.status = 'trialing');
`);

      if (!subscription[0]) {
        return res.status(404).json({ error: true, message: "No active subscription found" });
      }

      // Cancel subscription in Stripe
      const canceledSubscription = await stripe.cancelStripeSubscription({
        subscriptionId: subscription[0].stripe_id
      });

      // Update subscription status in database
      sdk.setTable("stripe_subscription");
      await sdk.update(
        {
          status: canceledSubscription.status
        },
        subscription[0].id
      );

      // Record cancellation in history
      sdk.setTable("subscription_history");
      await sdk.insert({
        user_id: userId,
        subscription_id: subscription[0].stripe_id,
        price_id: subscription[0].price_id,
        action: "canceled",
        status: canceledSubscription.status,
        details: JSON.stringify({
          plan_name: subscription[0].planName || "Unknown Plan",
          amount: subscription[0].amount || 0,
          currency: "usd",
          interval: subscription[0].interval || "month",
          cancel_at_period_end: canceledSubscription.cancel_at_period_end,
          canceled_at: new Date(canceledSubscription.canceled_at * 1000)
        }),
        create_at: new Date()
      });

      res.status(200).json({ error: false, message: "Subscription canceled successfully" });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });

  // Add API to change subscription
  app.post("/v3/api/custom/erpai/stripe/customer/subscription/change", middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      const { planId } = req.body;

      const validationResult = await ValidationService.validateObject(
        {
          planId: "required"
        },
        { planId }
      );

      if (validationResult.error) {
        return res.status(400).json(validationResult);
      }

      sdk.setProjectId(req.projectId);

      const userId = req.user_id;
      const userTable = `${sdk.getProjectId()}_user`;
      const stripeSubTable = `${sdk.getProjectId()}_stripe_subscription`;
      const stripePriceTable = `${sdk.getProjectId()}_stripe_price`;
      const subHistoryTable = `${sdk.getProjectId()}_subscription_history`;

      // Get active subscription
      const subscription = await sdk.rawQuery(`
    SELECT s.*, 
    ${jsonExtractor("s.object", "items.data[0].id")} as subItemId, 
    p.id AS planId, 
    p.name as planName, 
    p.amount, 
    ${jsonExtractor("p.object", "recurring.interval")} as \`interval\`
    FROM ${stripeSubTable} AS s 
    LEFT JOIN ${stripePriceTable} AS p ON s.price_id = p.id 
        WHERE s.user_id = ${userId} AND (s.status = 'active' OR s.status = 'trialing')
      `);

      if (!subscription[0]) {
        return res.status(404).json({ error: true, message: "No active subscription found" });
      }

      // Get new plan
      sdk.setTable("stripe_price");
      const newPlan = await sdk.get({ id: planId });
      if (!newPlan[0]) {
        return res.status(404).json({ error: true, message: "New plan not found" });
      }

      // Determine if this is an upgrade or downgrade
      const currentPlanAmount = subscription[0].amount || 0;
      const newPlanAmount = newPlan[0].amount || 0;
      const changeType = newPlanAmount > currentPlanAmount ? "upgrade" : newPlanAmount < currentPlanAmount ? "downgrade" : "change";

      // Update subscription in Stripe
      const updatedSubscription = await stripe.updateStripeSubscription({
        subscriptionId: subscription[0].stripe_id,
        subscriptionItemId: subscription[0].subItemId,
        newPriceId: newPlan[0].stripe_id
      });

      // Update subscription in database
      sdk.setTable("stripe_subscription");
      await sdk.update(
        {
          price_id: newPlan[0].id,
          status: updatedSubscription.status,
          object: JSON.stringify(updatedSubscription)
        },
        subscription[0].id
      );

      // Record change in history
      sdk.setTable("subscription_history");
      await sdk.insert({
        user_id: userId,
        subscription_id: subscription[0].stripe_id,
        price_id: newPlan[0].id,
        action: changeType,
        status: updatedSubscription.status,
        details: JSON.stringify({
          previous_plan: {
            id: subscription[0].planId,
            name: subscription[0].planName || "Unknown Plan",
            amount: subscription[0].amount || 0,
            currency: "usd",
            interval: subscription[0].interval || "month"
          },
          new_plan: {
            id: newPlan[0].id,
            name: newPlan[0].name || newPlan[0].nickname || "Unknown Plan",
            amount: newPlan[0].amount || 0,
            currency: "usd",
            interval: newPlan[0].recurring?.interval || "month"
          }
        }),
        create_at: new Date()
      });

      res.status(200).json({
        error: false,
        message: `Subscription ${changeType}d successfully`,
        subscription: updatedSubscription
      });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });

  // Add API to get subscription history
  app.get("/v2/api/lambda/stripe/customer/subscription/history", middlewares, TokenMiddleware(), async function (req, res) {
    try {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      const userId = req.user_id;
      const subHistoryTable = `${sdk.getProjectId()}_subscription_history`;

      // Get subscription history
      sdk.setTable("subscription_history");
      const history = await sdk.get({ user_id: userId }, { orderBy: "create_at", order: "DESC" });

      res.status(200).json({
        error: false,
        data: history
      });
    } catch (err) {
      console.error(err);
      let payload = {
        error: true,
        trace: err,
        message: err.message || "Something went wrong"
      };
      return res.status(500).json(payload);
    }
  });
};
