const { middlewares, public, TokenMiddleware } = require("../utils/middleware");
const { viewer_assessment_query_template } = require("../service/calculations");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  app.get("/v3/api/custom/erpai/viewer/assessment", [...middlewares, TokenMiddleware({ role: "viewer" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const user_id = req.user_id;

      let { assessment_id } = req.query;

      // Get query parameters

      // Get assessments with details

      let assessment = await sdk.rawQuery(`
  ${viewer_assessment_query_template(user_id, `WHERE va.user_id = ${user_id} AND a.id = ${assessment_id}`)}
    `);

      return res.status(200).json({
        error: false,
        message: "Assessment retrieved successfully",
        data: assessment.map((assessment) => ({
          id: assessment.id,
          name: assessment.name,
          description: assessment.description,
          frequency: assessment.frequency,
          duration: assessment.duration,
          start_date: assessment.start_date,
          current_period: assessment.current_period,
          next_assessment_date: assessment.next_assessment_date,
          status: assessment.status,
          is_favorite: Boolean(assessment.is_favorite),
          stats: {
            total_questions: assessment.total_questions,
            total_areas: assessment.total_areas,
            completed_questions: assessment.completed_questions,
            open_items: assessment.open_items,
            red_flags: assessment.red_flags,
            completion_percentage: assessment.total_questions > 0 ? Math.round((assessment.completed_questions / assessment.total_questions) * 100) : 0
          }
        }))
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v3/api/custom/erpai/viewer/assessments", middlewares, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      let { status = null, admin_ids = [], current_period = null, next_assessment_date = null, end_date = null, department_ids = [], favorite = 0 } = req.body;

      let user_id = 126; //req.user_id;

      // Build the status condition based on the calculated status

      let assessments = await sdk.rawQuery(`
        ${viewer_assessment_query_template(
          user_id,
          `WHERE va.user_id = ${user_id} 
           ${admin_ids.length > 0 ? `AND admin_id IN (${admin_ids.join(",")})` : ""} 
           ${current_period ? `AND current_period = ${current_period}` : ""} 
           ${end_date ? `AND DATE(end_date) = '${end_date}'` : ""} 
           ${next_assessment_date ? `AND DATE(next_assessment_date) = '${next_assessment_date}'` : ""} 
           ${department_ids.length > 0 ? `AND department_id IN (${department_ids.join(",")})` : ""}`
        )}
      `);

      if (favorite) {
        assessments = assessments.filter((assessment) => {
          const favouritesList = assessment.favourites_list || [];
          return favouritesList.includes(req.user_id);
        });
      }

      if (status) {
        assessments = assessments.filter((assessment) => assessment.status === status);
      }

      return res.status(200).json({ error: false, list: assessments });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Public viewer access to assessment results
  app.get("/v3/api/custom/erpai/viewer/assessment/:assessment_id", public, async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id } = req.params;
      let { viewer_token } = req.query;

      if (!viewer_token) {
        return res.status(401).json({ error: true, message: "Viewer token is required" });
      }

      // Verify viewer token
      sdk.setTable("viewer_access");
      let access = await sdk.get({ assessment_id, token: viewer_token, status: "active" });

      if (access.length === 0) {
        return res.status(403).json({ error: true, message: "Invalid or expired viewer token" });
      }

      // Get assessment data
      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      // Get basic statistics (limited data for external viewers)
      sdk.setTable("assessment_item");
      let items = await sdk.get({ assessment_id });
      let completedItems = items.filter((item) => item.answer && item.answer.trim() !== "");

      let viewerData = {
        assessment_name: assessment[0].name,
        total_questions: items.length,
        completed_questions: completedItems.length,
        completion_percentage: items.length > 0 ? Math.round((completedItems.length / items.length) * 100) : 0,
        last_updated: assessment[0].update_at || assessment[0].create_at
      };

      return res.status(200).json({ error: false, data: viewerData });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Generate viewer access token (for company/admin use)
  app.post("/v3/api/custom/erpai/viewer/generate-access", [...middlewares, TokenMiddleware({ role: "company|executive|admin" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { assessment_id, expires_at, viewer_email } = req.body;

      if (!assessment_id) {
        return res.status(400).json({ error: true, message: "Assessment ID is required" });
      }

      // Verify assessment exists and user has access
      sdk.setTable("assessment");
      let assessment = await sdk.get({ id: assessment_id });

      if (assessment.length === 0) {
        return res.status(404).json({ error: true, message: "Assessment not found" });
      }

      // Generate unique token
      const token = require("crypto").randomBytes(32).toString("hex");

      // Set default expiry (30 days from now)
      const expiryDate = expires_at ? new Date(expires_at) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

      sdk.setTable("viewer_access");
      let accessId = await sdk.insert({
        assessment_id,
        token,
        viewer_email: viewer_email || null,
        expires_at: expiryDate.toISOString(),
        status: "active",
        created_by: req.user_id
      });

      return res.status(200).json({
        error: false,
        message: "Viewer access created successfully",
        access_token: token,
        access_url: `/v3/api/custom/erpai/viewer/assessment/${assessment_id}?viewer_token=${token}`,
        expires_at: expiryDate.toISOString()
      });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Revoke viewer access
  app.delete(
    "/v3/api/custom/erpai/viewer/revoke-access/:access_id",
    [...middlewares, TokenMiddleware({ role: "company|executive|admin" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { access_id } = req.params;

        sdk.setTable("viewer_access");
        let access = await sdk.get({ id: access_id });

        if (access.length === 0) {
          return res.status(404).json({ error: true, message: "Viewer access not found" });
        }

        await sdk.update({ status: "revoked" }, access_id);

        return res.status(200).json({ error: false, message: "Viewer access revoked successfully" });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  // List viewer access tokens for an assessment
  app.get(
    "/v3/api/custom/erpai/viewer/list-access/:assessment_id",
    [...middlewares, TokenMiddleware({ role: "company|executive|admin" })],
    async (req, res) => {
      const sdk = req.sdk;
      sdk.setProjectId(req.projectId);

      try {
        let { assessment_id } = req.params;

        sdk.setTable("viewer_access");
        let accessList = await sdk.get({ assessment_id });

        // Hide actual tokens in the response for security
        let sanitizedList = accessList.map((access) => ({
          id: access.id,
          viewer_email: access.viewer_email,
          status: access.status,
          expires_at: access.expires_at,
          create_at: access.create_at,
          token_preview: access.token ? `${access.token.substring(0, 8)}...` : null
        }));

        return res.status(200).json({ error: false, list: sanitizedList });
      } catch (err) {
        errorCatcher(err, res);
      }
    }
  );

  console.log("Viewer routes module loaded");
};
