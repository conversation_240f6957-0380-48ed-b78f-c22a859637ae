const { middlewares, TokenMiddleware } = require("../utils/middleware");
const { errorCatcher } = require("../utils/errorHandler");

module.exports = function (app) {
  app.post("/v3/api/custom/erpai/worker/assigned-assessments", [...middlewares, TokenMiddleware({ role: "worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const user_id = req.user_id;
      // Destructure and sanitize input parameters
      const { status, start_date, end_date, current_period } = req.body;

      // Build the filter query with proper SQL injection protection
      let filterConditions = [];
      let queryParams = []; // Start with user_id as first parameter

      if (start_date) {
        filterConditions.push(`AND a.start_date = '${start_date}'`);
      }
      if (end_date) {
        filterConditions.push(`AND end_date = '${end_date}'`);
      }
      if (current_period) {
        filterConditions.push(`AND current_period = '${current_period}'`);
      }

      const filterQuery = filterConditions.join(" ");

      // Get assessments with assigned items for this worker
      let assessments = await sdk.rawQuery(`
  WITH calculated AS (
    SELECT *, 
    CASE 
      WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 1) + 1, duration)
      WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 7) + 1, duration)
      WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 30) + 1, duration)
      WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 365) + 1, duration)
      ELSE NULL
    END as initial_current_period,
    CASE 
      WHEN frequency = 'daily' THEN DATE_ADD(start_date, INTERVAL duration DAY)
      WHEN frequency = 'weekly' THEN DATE_ADD(start_date, INTERVAL duration WEEK)
      WHEN frequency = 'monthly' THEN DATE_ADD(start_date, INTERVAL duration MONTH)
      WHEN frequency = 'yearly' THEN DATE_ADD(start_date, INTERVAL duration YEAR)
      ELSE NULL
    END as end_date,
    CASE 
      WHEN frequency = 'daily' THEN DATE_ADD(CURDATE(), INTERVAL 1 DAY)
      WHEN frequency = 'weekly' THEN DATE_ADD(CURDATE(), INTERVAL 1 WEEK)
      WHEN frequency = 'monthly' THEN DATE_ADD(CURDATE(), INTERVAL 1 MONTH)
      WHEN frequency = 'yearly' THEN DATE_ADD(CURDATE(), INTERVAL 1 YEAR)
      ELSE NULL
    END as next_assessment_date
    FROM erpai_assessment
  ),
  closed_periods AS (
    SELECT assessment_id, MAX(period_id) as max_closed_period
    FROM erpai_assessment_closing_period
    GROUP BY assessment_id
  ),
  adjusted_period AS (
    SELECT c.*,
    CASE
      WHEN EXISTS (
        SELECT 1 FROM closed_periods cp
        WHERE cp.assessment_id = c.id
      ) THEN 
        (SELECT LEAST(cp.max_closed_period + 1, c.duration)
         FROM closed_periods cp
         WHERE cp.assessment_id = c.id)
      ELSE c.initial_current_period
    END as current_period
    FROM calculated c
  )
  SELECT DISTINCT
    a.*,
    ap.current_period,
    ap.end_date,
    ap.next_assessment_date,
    CASE 
      WHEN a.start_status = 1 THEN 'active'
      ELSE 'pending'
    END as status,
    JSON_CONTAINS(COALESCE(a.favourites_list, '[]'), JSON_ARRAY(${user_id})) as is_favorite,
    COUNT(DISTINCT ai.id) as total_assigned_items,
    SUM(CASE WHEN ai.answer = 'Yes' THEN 1 ELSE 0 END) as completed_items,
    COUNT(DISTINCT ai.id) - SUM(CASE WHEN ai.answer = 'Yes' THEN 1 ELSE 0 END) as incomplete_items,
    COUNT(CASE WHEN (ai.answer IS NULL OR ai.answer = "" OR ai.answer = "No") AND (ai.critical = 0 OR ai.critical IS NULL) AND ai.period_id = ap.current_period THEN 1 END) as open_items,
    COUNT(CASE WHEN (ai.answer IS NULL OR ai.answer = "") AND ai.critical = 1 AND ai.period_id = ap.current_period THEN 1 END) as red_flags,
    COUNT(DISTINCT ai.area_id) as assigned_areas_count,
    GROUP_CONCAT(DISTINCT area.name) as assigned_areas,
    u.id as admin_id,
    CONCAT(u.first_name, ' ', u.last_name) as admin_name
  FROM 
    erpai_assessment a
  JOIN 
    adjusted_period ap ON a.id = ap.id
  JOIN 
    erpai_assessment_item ai ON a.id = ai.assessment_id
   JOIN
    erpai_assessment_area area ON ai.area_id = area.id
  LEFT JOIN
    erpai_user u ON a.admin_id = u.id
  WHERE 
    ai.assigned_to = ${user_id}
    AND a.role_slug = 'worker'
    ${filterQuery}
  GROUP BY 
    a.id, a.favourites_list
  ORDER BY 
    a.create_at DESC
`);

      // filter status
      if (status) {
        assessments = assessments.filter((assessment) => assessment.status == status);
      }

      // Transform the data and handle null cases
      const transformedAssessments = assessments.map((assessment) => ({
        id: assessment.id,
        name: assessment.name,
        description: assessment.description || "",
        frequency: assessment.frequency,
        favourites_list: assessment.favourites_list,
        start_date: assessment.start_date,
        end_date: assessment.end_date,
        next_assessment_date: assessment.next_assessment_date,
        current_period: assessment.current_period,
        duration: assessment.duration,
        status: assessment.status,
        is_favorite: Boolean(assessment.is_favorite),
        total_assigned_items: parseInt(assessment.total_assigned_items) || 0,
        completed_items: parseInt(assessment.completed_items) || 0,
        incomplete_items: parseInt(assessment.incomplete_items) || 0,
        open_items: parseInt(assessment.open_items) || 0,
        red_flags: parseInt(assessment.red_flags) || 0,
        completion_percentage: assessment.total_assigned_items > 0 ? Math.round((assessment.completed_items / assessment.total_assigned_items) * 100) : 0,
        assigned_areas_count: parseInt(assessment.assigned_areas_count) || 0,
        assigned_areas: assessment.assigned_areas ? assessment.assigned_areas.split(",").filter(Boolean) : [],
        admin: assessment.admin_id
          ? {
              id: assessment.admin_id,
              name: assessment.admin_name || ""
            }
          : null
      }));

      return res.status(200).json({
        error: false,
        message: "Assigned assessments retrieved successfully",
        data: transformedAssessments
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({
        error: true,
        message: "An error occurred while retrieving assigned assessments",
        details: process.env.NODE_ENV === "development" ? err.message : undefined
      });
    }
  });
  // - /v3/api/custom/erpai/worker/assessment-questions
  app.post("/v3/api/custom/erpai/worker/validate-files/:question_id", [...middlewares, TokenMiddleware({ role: "worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);
    try {
      const { question_id } = req.params;
      const user_id = req.user_id;

      // Verify question ownership and get details
      const questionData = await sdk.rawQuery(`
          SELECT 
            ai.*,
            a.company_id,
            a.role_slug
          FROM 
            erpai_assessment_item ai
          JOIN 
            erpai_assessment a ON ai.assessment_id = a.id
          WHERE 
            ai.id = ${question_id}
            AND ai.assigned_to = ${user_id}
        `);

      if (!questionData.length) {
        return res.status(404).json({
          error: true,
          message: "Question not found or not assigned to you"
        });
      }

      const { files_url } = req.body;
      if (!Array.isArray(files_url) || files_url.length === 0) {
        return res.status(400).json({
          error: true,
          message: "Document link array is required"
        });
      }

      // Validate each file
      sdk.setTable("assessment_item");
      await sdk.updateWhere(
        {
          update_at: new Date().toISOString().slice(0, 19).replace("T", " ")
        },
        { id: question_id }
      );

      return res.status(200).json({
        error: false,
        message: "Files validated and saved successfully",
        data: {
          question_id
        }
      });
    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get worker tasks/questions
  app.get("/v3/api/custom/erpai/worker/my-tasks", [...middlewares, TokenMiddleware({ role: "worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let user_id = req.user_id;

      sdk.setTable("assessment_item");
      let tasks = await sdk.get({ assigned_to: user_id });

      return res.status(200).json({ error: false, list: tasks });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Submit answer
  app.post("/v3/api/custom/erpai/worker/submit-answer", [...middlewares, TokenMiddleware({ role: "worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let { question_id, answer, notes } = req.body;

      if (!question_id || !answer) {
        return res.status(400).json({ error: true, message: "Question ID and answer are required" });
      }

      // Verify the question is assigned to this worker
      sdk.setTable("assessment_item");
      let question = await sdk.get({ id: question_id, assigned_to: req.user_id });

      if (question.length === 0) {
        return res.status(404).json({ error: true, message: "Question not found or not assigned to you" });
      }

      await sdk.update({ answer, notes: notes || null }, question_id);

      return res.status(200).json({ error: false, message: "Answer submitted successfully" });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  // Get worker profile/stats
  app.get("/v3/api/custom/erpai/worker/profile", [...middlewares, TokenMiddleware({ role: "worker" })], async (req, res) => {
    const sdk = req.sdk;
    sdk.setProjectId(req.projectId);

    try {
      let user_id = req.user_id;

      sdk.setTable("user");
      let user = await sdk.get({ id: user_id });

      if (user.length === 0) {
        return res.status(404).json({ error: true, message: "User not found" });
      }

      // Get task statistics
      sdk.setTable("assessment_item");
      let allTasks = await sdk.get({ assigned_to: user_id });
      let completedTasks = allTasks.filter((task) => task.answer && task.answer.trim() !== "");

      let profile = {
        ...user[0],
        stats: {
          total_tasks: allTasks.length,
          completed_tasks: completedTasks.length,
          completion_rate: allTasks.length > 0 ? Math.round((completedTasks.length / allTasks.length) * 100) : 0
        }
      };

      return res.status(200).json({ error: false, profile });
    } catch (err) {
      errorCatcher(err, res);
    }
  });

  console.log("Worker routes module loaded");
};
