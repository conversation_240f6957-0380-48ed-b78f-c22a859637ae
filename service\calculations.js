async function calculateProblematicAreas(sdk, company_id, date = new Date().toISOString().split("T")[0]) {
  let rawQuery = `
    WITH calculated AS (
      SELECT *, 
      CASE 
        WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 1) + 1, duration)
        WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 7) + 1, duration)
        WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 30) + 1, duration)
        WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 365) + 1, duration)
        ELSE NULL
      END as initial_current_period
      FROM erpai_assessment
    ),
    closed_periods AS (
      SELECT assessment_id, period_id
      FROM erpai_assessment_closing_period
    ),
    adjusted_period AS (
      SELECT 
        c.*,
        (
          SELECT COALESCE(MAX(period_id) + 1, c.initial_current_period)
          FROM closed_periods
          WHERE assessment_id = c.id AND period_id < c.duration
        ) as current_period
      FROM calculated c
    ),
    combined AS (
      SELECT 
        erpai_assessment_area.name,
        ap.company_id,
        COUNT(CASE 
          WHEN (erpai_assessment_item.answer IS NULL 
            OR erpai_assessment_item.answer = "" 
            OR erpai_assessment_item.answer = "No") 
          AND (erpai_assessment_item.critical = 0 
            OR erpai_assessment_item.critical IS NULL) 
          AND period_id = ap.current_period 
          AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
          THEN 1 
        END) as open_items,
        COUNT(CASE 
          WHEN (erpai_assessment_item.answer IS NULL 
            OR erpai_assessment_item.answer = "") 
          AND erpai_assessment_item.critical = 1 
          AND period_id <= ap.current_period 
          AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
          THEN 1 
        END) as red_flags
      FROM erpai_assessment_area
      LEFT JOIN erpai_assessment_item ON erpai_assessment_area.id = erpai_assessment_item.area_id
      LEFT JOIN adjusted_period ap ON erpai_assessment_item.assessment_id = ap.id
      GROUP BY erpai_assessment_area.name
    )
    SELECT * FROM combined
    WHERE company_id = ${company_id}
    ORDER BY (open_items + red_flags) DESC, red_flags DESC
  `;
  let areas = await sdk.rawQuery(rawQuery);
  return areas;
}

async function calculateQuestionStats(sdk, company_id, date = new Date().toISOString().split("T")[0]) {
  let rawQuery = `
    WITH calculated AS (
      SELECT *, 
      CASE 
        WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 1) + 1, duration)
        WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 7) + 1, duration)
        WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 30) + 1, duration)
        WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 365) + 1, duration)
        ELSE NULL
      END as initial_current_period
      FROM erpai_assessment
    ),
    closed_periods AS (
      SELECT assessment_id, period_id
      FROM erpai_assessment_closing_period
    ),
    adjusted_period AS (
      SELECT 
        c.*,
        (
          SELECT COALESCE(MAX(period_id) + 1, c.initial_current_period)
          FROM closed_periods
          WHERE assessment_id = c.id AND period_id < c.duration
        ) as current_period
      FROM calculated c
    ),
    combined AS (
      SELECT 
        erpai_assessment_item.question,
        CASE WHEN erpai_assessment_item.critical = 1 THEN "Yes" ELSE "No" END as critical,
        ap.company_id,
        COUNT(CASE 
          WHEN (erpai_assessment_item.answer IS NULL OR erpai_assessment_item.answer = "") 
          AND erpai_assessment_item.period_id <= ap.current_period 
          AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
          THEN 1 
        END) as unanswered_count,
        COUNT(CASE 
          WHEN (erpai_assessment_item.answer = "no") 
          AND erpai_assessment_item.period_id <= ap.current_period 
          AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
          THEN 1 
        END) as answered_no_count,
        GROUP_CONCAT(
          DISTINCT CONCAT(erpai_user.id, ':', erpai_user.first_name, ' ', erpai_user.last_name) 
          ORDER BY erpai_user.id ASC 
          SEPARATOR ','
        ) as users_list
      FROM erpai_assessment_item
      LEFT JOIN adjusted_period ap ON erpai_assessment_item.assessment_id = ap.id
      LEFT JOIN erpai_user ON erpai_assessment_item.assigned_to = erpai_user.id
      WHERE erpai_assessment_item.period_id <= ap.current_period
      GROUP BY erpai_assessment_item.question
    )
    SELECT * FROM combined
    WHERE company_id = ${company_id}
    ORDER BY (unanswered_count + answered_no_count) DESC, unanswered_count DESC
  `;

  let questions = await sdk.rawQuery(rawQuery);
  for (let question of questions) {
    console.log("question.users_list", question.users_list);
    if (question.users_list) {
      question.users_list = question.users_list.split(",").map((user) => {
        let user_details = user.split(":");
        return {
          id: user_details[0],
          name: user_details[1]
        };
      });
    } else {
      question.users_list = [];
    }
  }
  return questions;
}

async function calculateUserStats(sdk, company_id, date = new Date().toISOString().split("T")[0]) {
  let rawQuery = `
  WITH calculated AS (
      SELECT *, 
      CASE
          WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 1) + 1, duration)
          WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 7) + 1, duration)
          WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 30) + 1, duration)
          WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 365) + 1, duration)
          ELSE NULL
      END as initial_current_period
      FROM erpai_assessment
  ),
  adjusted_period AS (
      SELECT c.*,
      CASE
          -- Check if there's a closed assessment_closing_period for the current period
          WHEN EXISTS (
              SELECT 1 FROM erpai_assessment_closing_period acp
              WHERE acp.assessment_id = c.id
              AND acp.period_id = c.initial_current_period
          ) THEN c.initial_current_period + 1
          ELSE c.initial_current_period
      END as current_period
      FROM calculated c
  ),
  combined AS (
      SELECT 
          CONCAT(erpai_user.first_name, ' ', erpai_user.last_name) as name,
          COALESCE(erpai_position.name, erpai_user.role_slug) as role,
          erpai_user.company, 
          COUNT(CASE 
              WHEN (erpai_assessment_item.answer IS NULL OR erpai_assessment_item.answer = "") 
              AND (erpai_assessment_item.critical = 0 OR erpai_assessment_item.critical IS NULL) 
              AND period_id <= ap.current_period 
              THEN 1 
          END) as open_items,
          COUNT(CASE 
              WHEN (erpai_assessment_item.answer IS NULL OR erpai_assessment_item.answer = "") 
              AND erpai_assessment_item.critical = 1 
              AND period_id <= ap.current_period 
              THEN 1 
          END) as red_flags
      FROM erpai_user
      LEFT JOIN erpai_assessment_item ON erpai_user.id = erpai_assessment_item.assigned_to
      LEFT JOIN adjusted_period ap ON erpai_assessment_item.assessment_id = ap.id
      LEFT JOIN erpai_position ON erpai_user.position_id = erpai_position.id
      WHERE erpai_user.role_slug != "admin" AND erpai_user.role_slug != "executive"
      GROUP BY erpai_user.id, erpai_user.company, erpai_position.name, erpai_user.role_slug
  )
  SELECT * FROM combined
  WHERE company = ${company_id}
  ORDER BY (open_items + red_flags) DESC, red_flags DESC;
  `;

  return await sdk.rawQuery(rawQuery);
}

async function calculateUserStatsByAssessment(sdk, company_id, assessment_id, date = new Date().toISOString().split("T")[0]) {
  let rawQuery = `WITH calculated AS (
    SELECT erpai_assessment.*, 
    CONCAT(erpai_user.first_name, ' ', erpai_user.last_name) as admin_name,
    erpai_department.name as department_name,
    CASE 
      WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 1) + 1, duration)
      WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 7) + 1, duration)
      WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 30) + 1, duration)
      WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF('${date}', start_date) / 365) + 1, duration)
      ELSE NULL
    END as initial_current_period,
    CASE 
      WHEN frequency = 'daily' THEN DATE_ADD(start_date, INTERVAL duration DAY)
      WHEN frequency = 'weekly' THEN DATE_ADD(start_date, INTERVAL duration WEEK)
      WHEN frequency = 'monthly' THEN DATE_ADD(start_date, INTERVAL duration MONTH)
      WHEN frequency = 'yearly' THEN DATE_ADD(start_date, INTERVAL duration YEAR)
      ELSE NULL
    END as end_date
    FROM erpai_assessment
    LEFT JOIN erpai_user ON erpai_assessment.admin_id = erpai_user.id
    LEFT JOIN erpai_department ON erpai_assessment.department_id = erpai_department.id
),
adjusted_period AS (
    SELECT c.*,
    CASE
      -- Check if there's a closed assessment_closing_period for the current period
      WHEN EXISTS (
        SELECT 1 FROM erpai_assessment_closing_period acp
        WHERE acp.assessment_id = c.id
        AND acp.period_id = c.initial_current_period
      ) THEN c.initial_current_period + 1
      ELSE c.initial_current_period
    END as current_period
    FROM calculated c
),
combined AS (
    SELECT 
      CONCAT(erpai_user.first_name, ' ', erpai_user.last_name) as name,
      ap.company_id,
      ap.admin_name,
      ap.department_name,
      erpai_assessment_item.assessment_id,
      ap.name as assessment_name,
      ap.start_date,
      ap.end_date,
      ap.frequency,
      ap.duration,
      CASE 
        WHEN ap.start_status = 1 AND ap.end_date < '${date}' THEN 'completed'
        WHEN ap.start_status = 1 THEN 'active'
        ELSE 'pending'
      END as status,
      COUNT(CASE 
        WHEN (erpai_assessment_item.answer IS NULL 
          OR erpai_assessment_item.answer = "" 
          OR erpai_assessment_item.answer = "No") 
        AND (erpai_assessment_item.critical = 0 
          OR erpai_assessment_item.critical IS NULL) 
        AND period_id <= ap.current_period 
        AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
        THEN 1 
      END) as open_items,
      COUNT(CASE 
        WHEN (erpai_assessment_item.answer IS NULL 
          OR erpai_assessment_item.answer = "") 
        AND erpai_assessment_item.critical = 1 
        AND period_id <= ap.current_period 
        AND (erpai_assessment_item.moved = 0 OR erpai_assessment_item.moved IS NULL)
        THEN 1 
      END) as red_flags,
      COUNT(CASE 
        WHEN erpai_assessment_item.period_id = ap.current_period 
        THEN 1 
      END) as total_assigned_items,
      COUNT(CASE 
        WHEN erpai_assessment_item.answer IS NOT NULL 
        AND erpai_assessment_item.answer != "" 
        AND period_id <= ap.current_period 
        THEN 1 
      END) as completed_items,
      GROUP_CONCAT(
        DISTINCT erpai_assessment_area.name 
        ORDER BY erpai_assessment_area.name ASC 
        SEPARATOR ','
      ) as area_list
    FROM erpai_assessment_item
    LEFT JOIN adjusted_period ap ON erpai_assessment_item.assessment_id = ap.id
    LEFT JOIN erpai_user ON erpai_assessment_item.assigned_to = erpai_user.id
    LEFT JOIN erpai_assessment_area ON erpai_assessment_item.area_id = erpai_assessment_area.id
    GROUP BY erpai_user.id, erpai_assessment_item.assessment_id
)
SELECT * FROM combined
WHERE company_id = ${company_id}
${assessment_id ? `AND assessment_id = ${assessment_id}` : ""}
ORDER BY combined.start_date DESC
`;

  let users = await sdk.rawQuery(rawQuery);

  let assessment_map = {};

  let result = [];

  let cursor = 0;

  for (let user of users) {
    if (assessment_map[user.assessment_id] == undefined) {
      assessment_map[user.assessment_id] = cursor;
      result.push({
        assessment_id: user.assessment_id,
        red_flags: user.red_flags,
        open_items: user.open_items,
        completed_items: user.completed_items,
        total_assigned_items: user.total_assigned_items,
        assessment_name: user.assessment_name,
        start_date: user.start_date,
        end_date: user.end_date,
        frequency: user.frequency,
        duration: user.duration,
        status: user.status,
        admin_name: user.admin_name,
        department_name: user.department_name,
        user_details: [
          {
            name: user.name,
            total_assigned_items: user.total_assigned_items,
            completed_items: user.completed_items,
            area_list: user.area_list ? user.area_list.split(",") : []
          }
        ]
      });
      cursor++;
    } else {
      let index = assessment_map[user.assessment_id];
      console.log(index);
      result[index].user_details.push({
        name: user.name,
        total_assigned_items: user.total_assigned_items,
        completed_items: user.completed_items,
        area_list: user.area_list ? user.area_list.split(",") : []
      });
      result[index].total_assigned_items += user.total_assigned_items;
      result[index].completed_items += user.completed_items;
      result[index].red_flags += user.red_flags;
      result[index].open_items += user.open_items;
    }
  }

  return result;
}

async function getDataForAI(sdk, type, options = {}) {
  if (type == "problematic-areas") {
    return await calculateProblematicAreas(sdk, options.company_id, options.date ?? new Date().toISOString().split("T")[0]);
  } else if (type == "question-stats") {
    return await calculateQuestionStats(sdk, options.company_id, options.date ?? new Date().toISOString().split("T")[0]);
  } else if (type == "user-stats") {
    return await calculateUserStats(sdk, options.company_id, options.date ?? new Date().toISOString().split("T")[0]);
  } else if (type == "user-stats-by-assessment") {
    return await calculateUserStatsByAssessment(sdk, options.company_id, options.assessment_id ?? null, options.date ?? new Date().toISOString().split("T")[0]);
  }
}

async function getAssessmentDetails(sdk, company_id, assessment_id) {
  let rawQuery = `WITH calculated AS (
          SELECT *, 
          CASE 
            WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 1) + 1, duration)
            WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 7) + 1, duration)
            WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 30) + 1, duration)
            WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 365) + 1, duration)
            ELSE NULL
          END as initial_current_period
          FROM erpai_assessment
        ),
        adjusted_period AS (
          SELECT c.*,
          CASE
            WHEN EXISTS (
              SELECT 1 FROM erpai_assessment_closing_period acp
              WHERE acp.assessment_id = c.id
              AND acp.period_id = c.initial_current_period
            ) THEN c.initial_current_period + 1
            ELSE c.initial_current_period
          END as current_period
          FROM calculated c
        ),
        combined AS (
              SELECT 
                     ap.company_id,
                     ap.id as assessment_id,
                     erpai_assessment_area.name,
                     erpai_assessment_item.period_id,
                     ap.name as assessment_name,
                     COUNT(CASE WHEN erpai_assessment_item.period_id <= ap.current_period THEN 1 END) as total_assigned_items,
                     COUNT(CASE WHEN erpai_assessment_item.answer = 'Yes' AND erpai_assessment_item.period_id <= ap.current_period THEN 1 END) as completed_items,
                     COUNT(CASE WHEN (erpai_assessment_item.answer IS NULL OR erpai_assessment_item.answer = '' OR erpai_assessment_item.answer = 'No') 
                          AND (erpai_assessment_item.critical = 0 OR erpai_assessment_item.critical IS NULL) 
                          AND period_id = ap.current_period THEN 1 END) as open_items,
                     COUNT(CASE WHEN (erpai_assessment_item.answer IS NULL OR erpai_assessment_item.answer = '') 
                          AND erpai_assessment_item.critical = 1 
                          AND period_id = ap.current_period THEN 1 END) as red_flags
              FROM erpai_assessment_area
              LEFT JOIN adjusted_period ap ON erpai_assessment_area.assessment_id = ap.id
              LEFT JOIN erpai_assessment_item ON erpai_assessment_area.id = erpai_assessment_item.area_id
              WHERE erpai_assessment_area.assessment_id = ${assessment_id}
              GROUP BY erpai_assessment_area.name, erpai_assessment_item.period_id
              HAVING erpai_assessment_item.period_id IS NOT NULL
              ORDER BY erpai_assessment_item.period_id, erpai_assessment_area.name
          )
        SELECT * FROM combined
        WHERE company_id = ${company_id}`;

  let data = await sdk.rawQuery(rawQuery);

  // aggregate for areas with a list for each period

  let area_map = {};
  let result = [];
  cursor = 0;

  for (let row of data) {
    if (area_map[row.name] == undefined) {
      area_map[row.name] = cursor;
      result.push({
        name: row.name,
        total_assigned_items: row.total_assigned_items,
        completed_items: row.completed_items,
        period_list: [
          {
            period_id: row.period_id,
            total_assigned_items: row.total_assigned_items,
            completed_items: row.completed_items,
            open_items: row.open_items,
            red_flags: row.red_flags
          }
        ]
      });
      cursor++;
    } else {
      let index = period_map[row.period_id];
      result[index].total_assigned_items += row.total_assigned_items;
      result[index].completed_items += row.completed_items;
      result[index].period_list.push({
        period_id: row.period_id,
        total_assigned_items: row.total_assigned_items,
        completed_items: row.completed_items,
        open_items: row.open_items,
        red_flags: row.red_flags
      });
    }
  }

  sdk.setTable("erpai_settings");
  let settings = await sdk.get({ company: company_id });

  let { period_type } = settings[0];

  return result;
}

async function getAssessmentDetailsV2(sdk, assessment_id) {
  sdk.setTable("assessment");
  let assessment = (await sdk.get({ id: assessment_id }))[0];

  sdk.setTable("assessment_item");
  let items = await sdk.get({ assessment_id });

  if (items.length == 0) {
    return {
      assessment: assessment,
      areas: [],
      period_totals: []
    };
  }

  sdk.setTable("assessment_area");
  let areas = await sdk.get({ assessment_id });

  let area_map = {};

  let idx = 0;

  if (areas.length == 0) {
    return {
      assessment: assessment,
      areas: [],
      period_totals: []
    };
  }

  for (let area of areas) {
    area_map[area.id] = idx;
    areas[idx].period_list = [];
    areas[idx].open_items = 0;
    areas[idx].red_flags = 0;
    for (let period_id = 1; period_id <= assessment.duration; period_id++) {
      areas[idx].period_list.push({
        period_id: period_id,
        total_assigned_items: 0,
        completed_items: 0,
        open_items: [], // { period_id: period_id, create_at: period in date format, item_id: item.id }
        red_flags: [] // { period_id: period_id, create_at: period in date format, item_id: item.id }
      });
    }
    idx++;
  }

  sdk.setTable("settings");
  let settings = await sdk.get({ company_id: assessment.company_id });
  let { area_health_green, area_health_yellow, area_health_red } = { area_health_green: 90, area_health_yellow: 75, area_health_red: 50 };
  if (settings.length > 0) {
    ({ area_health_green, area_health_yellow, area_health_red } = settings[0]);
  }

  let current_period = await calculatePeriodId(sdk, assessment_id);

  for (let item of items) {
    area_idx = area_map[item.area_id];

    // Only process items for their specific period
    if (item.period_id <= current_period) {
      // Get the period index (0-based)
      const periodIdx = item.period_id - 1;

      // Always increment total assigned items for the item's specific period
      areas[area_idx].period_list[periodIdx].total_assigned_items++;

      if (item.answer === "Yes") {
        // If answered Yes, increment completed items for this period
        areas[area_idx].period_list[periodIdx].completed_items++;
      } else if (item.moved == 1) {
        // If moved, don't count as open or red flag
        // But don't increment completed_items either
      } else if (item.critical) {
        // If critical, not moved, and not answered Yes, add to red flags
        areas[area_idx].period_list[periodIdx].red_flags.push({
          period_id: item.period_id,
          create_at: item.create_at,
          item_id: item.id
        });
        areas[area_idx].red_flags++;
      } else {
        // If not critical, not moved, and not answered Yes, add to open items
        areas[area_idx].period_list[periodIdx].open_items.push({
          period_id: item.period_id,
          create_at: item.create_at,
          item_id: item.id
        });
        areas[area_idx].open_items++;
      }
    }
  }

  let period_totals = [];

  for (let period_id = 1; period_id <= assessment.duration; period_id++) {
    period_totals.push({
      period_id: period_id,
      total_assigned_items: 0,
      completed_items: 0,
      open_items: 0,
      red_flags: 0
    });
  }

  for (let area of areas) {
    for (let period of area.period_list) {
      period.total_assigned_items = period.total_assigned_items || 0;
      period.completed_items = period.completed_items || 0;
      period.open_items = period.open_items || [];
      period.red_flags = period.red_flags || [];

      period.percentage = period.total_assigned_items > 0 ? parseFloat(((period.completed_items / period.total_assigned_items) * 100).toFixed(2)) : 0;

      period.health = period.percentage >= area_health_green ? "green" : period.percentage >= area_health_yellow ? "yellow" : "red";

      const periodIdx = period.period_id - 1;
      // Add counts for this specific period to the period totals
      period_totals[periodIdx].total_assigned_items += period.total_assigned_items;
      period_totals[periodIdx].completed_items += period.completed_items;
      period_totals[periodIdx].open_items += period.open_items.length;
      period_totals[periodIdx].red_flags += period.red_flags.length;
    }
  }

  for (let period of period_totals) {
    period.percentage = period.total_assigned_items > 0 ? parseFloat(((period.completed_items / period.total_assigned_items) * 100).toFixed(2)) : 0;

    period.health = period.percentage >= area_health_green ? "green" : period.percentage >= area_health_yellow ? "yellow" : "red";
  }

  return {
    assessment: assessment,
    areas: areas,
    period_totals: period_totals
  };
}

async function getDepartments(sdk, company_id) {
  let departments = await sdk.rawQuery(`SELECT erpai_department.*,  COUNT(erpai_assessment.id) as assessment_count 
    FROM erpai_department
    LEFT JOIN erpai_assessment ON erpai_department.id = erpai_assessment.department_id
    WHERE erpai_department.company_id = ${company_id}
    GROUP BY department_id
    `);

  return departments;
}

async function calculatePeriodId(sdk, assessment_id) {
  let assessment = await sdk.rawQuery(`
WITH calculated AS (
    SELECT *,
    CASE
        WHEN frequency = 'daily' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 1) + 1, duration)
        WHEN frequency = 'weekly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 7) + 1, duration)
        WHEN frequency = 'monthly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 30) + 1, duration)
        WHEN frequency = 'yearly' THEN LEAST(FLOOR(DATEDIFF(CURDATE(), start_date) / 365) + 1, duration)
        ELSE NULL
    END as initial_current_period
    FROM erpai_assessment
    WHERE id = ${assessment_id}
),
closed_periods AS (
    SELECT period_id
    FROM erpai_assessment_closing_period
    WHERE assessment_id = ${assessment_id}
),
adjusted_period AS (
    SELECT 
        c.*,
        (
            SELECT COALESCE(MAX(period_id) + 1, c.initial_current_period)
            FROM closed_periods
            WHERE period_id < c.duration
        ) as current_period
    FROM calculated c
)
SELECT current_period FROM adjusted_period;`);

  return assessment[0].current_period;
}

async function getItems(sdk, assessment_id, period_id) {
  let current_period = await calculatePeriodId(sdk, assessment_id);
  sdk.setTable("settings");
  let settings = await sdk.get({ company_id: assessment_id });

  let items = await sdk.rawQuery(`
    SELECT * FROM erpai_assessment_item
    WHERE assessment_id = ${assessment_id}
    AND (period_id = ${period_id}
    OR ((answer is NULL
    OR answered_period >= ${period_id}) AND period_id <= ${current_period}))
    `);
  return items;
}

module.exports = {
  calculateProblematicAreas,
  calculateQuestionStats,
  calculateUserStats,
  calculateUserStatsByAssessment,
  getDataForAI,
  getAssessmentDetails,
  getDepartments,
  getItems,
  calculatePeriodId,
  getAssessmentDetailsV2
};
