const MailService = require("../../../services/MailService");
const config = require("../../../config");

async function sendNotification(sdk, user_id, role) {
  sdk.setTable("user");
  let user = await sdk.get({ id: user_id });
  if (user.length == 0) {
    throw new Error("User not found");
  }

  let company_id = "";

  if (role == "company") {
    company_id = user_id;
  } else {
    company_id = user[0].company;
  }

  sdk.setTable("user");

  let recipients = await sdk.get({ company: company_id, role: "executive" });

  let company = await sdk.get({ id: company_id });

  recipients = recipients.concat(company);

  MailService.initialize(config);

  let template = await MailService.template("sop-approval-request", sdk);

  for (let recipient of recipients) {
    let content = MailService.inject({ body: template.message.html, subject: template.message.subject }, {});
    await MailService.send(config.from_mail, recipient.email, content.subject, content.html);
  }

  return true;
}

module.exports = {
  sendNotification
};
