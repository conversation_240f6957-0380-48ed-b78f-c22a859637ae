// sopGenerator.js - Generates HTML content for SOPs
const generateSOPHTML = (sop_details, vision, department) => {
  //departmentData = [{
  //  department_name: "department_id",
  //  goals: [
  //   {
  //   "goal_title": "Customer Satisfaction",
  //   "goal_description": "Manage accounts effectively to enhance customer satisfaction and retention."
  // }
  //  ]
  //}]
  // Helper function to format role SOPs
  const formatSOPs = (sops) => {
    let html = "";

    if (sops.must && sops.must.length > 0) {
      html += "<h4>MUST:</h4><ul>";
      sops.must.forEach((item) => {
        html += `<li>${item}</li>`;
      });
      html += "</ul>";
    }

    if (sops.shall && sops.shall.length > 0) {
      html += "<h4>SHALL:</h4><ul>";
      sops.shall.forEach((item) => {
        html += `<li>${item}</li>`;
      });
      html += "</ul>";
    }

    if (sops.will && sops.will.length > 0) {
      html += "<h4>WILL:</h4><ul>";
      sops.will.forEach((item) => {
        html += `<li>${item}</li>`;
      });
      html += "</ul>";
    }

    return html;
  };

  // Generate the main HTML content
  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>ERGO SOP Document</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2c3e50;
        }
        h1 {
          color: #2c3e50;
          text-align: center;
          margin-bottom: 30px;
        }
        h2 {
          color: #2c3e50;
          border-bottom: 2px solid #3498db;
          padding-bottom: 5px;
          margin-top: 25px;
        }
        h3 {
          color: #34495e;
          margin-top: 20px;
        }
        h4 {
          margin-bottom: 5px;
          margin-top: 15px;
          color: #2980b9;
        }
        p {
          margin-bottom: 15px;
        }
        ul {
          margin-top: 5px;
          margin-bottom: 20px;
        }
        li {
          margin-bottom: 8px;
        }
        .section {
          margin-bottom: 30px;
        }
        .role {
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          border-left: 4px solid #3498db;
          margin-bottom: 20px;
        }
        .role-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        .role-id {
          color: #7f8c8d;
          font-size: 14px;
        }
        .page-break {
          page-break-after: always;
        }
        .footer {
          text-align: center;
          margin-top: 50px;
          font-size: 12px;
          color: #7f8c8d;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">ERGO</div>
          <h1>ERGO 2024 VISION</h1>
        </div>
        
        ${
          vision
            ? `
        <div class="section">
          <h2>VISION</h2>
          <p>${vision}</p>
        </div>
        <div class="page-break"></div>
        `
            : ""
        }
        
        
        <div class="section">
          <h2>RESPONSIBILITIES</h2>
  `;

  // Add each role's details
  sop_details.forEach((role) => {
    html += `
      <div class="role">
        <div class="role-header">
          <h3>${role.role.toUpperCase()}</h3>
        </div>
        <p>${role?.narrative || ""}</p>
        ${formatSOPs(role?.sops || {})}
      </div>
    `;
  });

  // Add department details
  html += `
    <div class="section">
      <h2>GOALS:</h2>
      <ul>
        ${department
          .map(
            (dept) => `
            <li>${dept.department_name}</li>
            <ul>
                ${dept.goals
                  .map(
                    (goal) => `
                    <li>${goal.goal_title}</li>
                `
                  )
                  .join("")}
            </ul>
        `
          )
          .join("")}
      </ul>
    </div>
  `;

  // Add footer
  html += `
      </div>
    </body>
    </html>
  `;

  return html;
};

module.exports = { generateSOPHTML };
