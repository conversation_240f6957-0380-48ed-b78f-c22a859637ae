// Add this helper function for subscription limits
async function checkSubscriptionLimits(sdk, company_id, checkType, currentCount = 0) {
  try {
    // Get the active subscription for the company
    sdk.setTable("stripe_subscription");

    const subscriptions = await sdk.rawQuery(`
      SELECT * FROM erpai_stripe_subscription 
      WHERE user_id = ${company_id} 
      AND (status = 'active' OR status = 'trialing')
    `);

    if (subscriptions.length === 0) {
      return { allowed: false, message: "No active subscription found" };
    }

    // Get the price details for the subscription
    sdk.setTable("stripe_price_details");
    const priceDetails = await sdk.get({
      price_id: subscriptions[0].price_id
    });

    if (priceDetails.length === 0) {
      return { allowed: false, message: "Subscription details not found" };
    }

    // Check limits based on type
    switch (checkType) {
      case "department":
        if (currentCount > priceDetails[0].department_allowed) {
          return {
            allowed: false,
            message: `You have reached the maximum limit of ${priceDetails[0].department_allowed} departments for your subscription plan`
          };
        }
        break;
      case "assessment":
        if (currentCount > priceDetails[0].assessment_allowed) {
          return {
            allowed: false,
            message: `You have reached the maximum limit of ${priceDetails[0].assessment_allowed} assessments for your subscription plan`
          };
        }
        break;
      case "user":
        // For users, we check against max_company_size
        if (currentCount > priceDetails[0].user_allowed) {
          return {
            allowed: false,
            message: `You have reached the maximum team size of ${priceDetails[0].max_company_size} for your subscription plan`
          };
        }
      case "position":
        if (currentCount > priceDetails[0].max_company_size) {
          return {
            allowed: false,
            message: `You have reached the maximum team size of ${priceDetails[0].max_company_size} for your subscription plan`
          };
        }
        break;
    }

    return { allowed: true };
  } catch (err) {
    console.error("Error checking subscription limits:", err);
    return { allowed: false, message: "Error checking subscription limits" };
  }
}

module.exports = {
  checkSubscriptionLimits
};
