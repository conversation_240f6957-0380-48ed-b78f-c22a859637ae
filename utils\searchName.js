const searchFullName = async (search, sdk) => {
  // Split the search term into words
  const searchTerms = search.trim().split(/\s+/);

  // Create the base query with string interpolation
  let query = `
    SELECT * FROM erpai_user 
    WHERE CONCAT(first_name, ' ', last_name) LIKE "%${search}%"
    OR first_name LI<PERSON> "%${search}%" 
    OR last_name LIKE "%${search}%"
  `;

  // If there are multiple words, add additional search conditions
  if (searchTerms.length > 1) {
    query += `
      OR (first_name LIKE "%${searchTerms[0]}%" 
          AND last_name LIKE "%${searchTerms[1]}%")
    `;
  }

  // Execute the query
  const users = await sdk.rawQuery(query);
  return users;
};

module.exports = { searchFullName };
